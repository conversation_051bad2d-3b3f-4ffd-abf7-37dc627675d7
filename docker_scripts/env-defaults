#!/bin/bash

PG_SSL=${PG_SSL:-}

PG_TRUST_LOCALNET=${PG_TRUST_LOCALNET:-$PSQL_TRUST_LOCALNET} # backward compatibility
PG_TRUST_LOCALNET=${PG_TRUST_LOCALNET:-false}

DB_NAME=${DB_NAME:-}
DB_USER=${DB_USER:-}
DB_PASS=${DB_PASS:-}
DB_TEMPLATE=${DB_TEMPLATE:-template1}

DB_EXTENSION=${DB_EXTENSION:-}

# Defaults for the toolkit
export NEXT_PUBLIC_API_HOSTNAME=${NEXT_PUBLIC_API_HOSTNAME:-http://localhost:8000}
export FRONTEND_HOSTNAME=${FRONTEND_HOSTNAME:-http://localhost:4000}
export NEXT_PUBLIC_FRONTEND_HOSTNAME=${FRONTEND_HOSTNAME:-http://localhost:4000}
export PYTHON_INTERPRETER_URL=${PYTHON_INTERPRETER_URL:-http://localhost:8080}
export DATABASE_URL=${DATABASE_URL:-postgresql+psycopg2://postgre:postgre@localhost:5432/toolkit}
export INSTALL_COMMUNITY_DEPS=${INSTALL_COMMUNITY_DEPS:false}
export API_HOSTNAME=${API_HOSTNAME:-http://localhost:8000}