{"openapi": "3.1.0", "info": {"title": "Cohere Toolkit API", "description": "This is the API for the Open Source Cohere Toolkit", "version": "v1.1.6"}, "paths": {"/v1/auth_strategies": {"get": {"tags": ["auth"], "summary": "Get Strategies", "description": "Retrieves the currently enabled list of Authentication strategies.", "operationId": "get_strategies_v1_auth_strategies_get", "parameters": [{"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ListAuthStrategy"}, "title": "Response Get Strategies V1 Auth Strategies Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/login": {"post": {"tags": ["auth"], "summary": "<PERSON><PERSON>", "description": "Logs user in, performing basic email/password auth.\nVerifies their credentials, retrieves the user and returns a JWT token.\n\nRaises:\n    HTTPException: If the strategy or payload are invalid, or if the login fails.", "operationId": "login_v1_login_post", "parameters": [{"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Login"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"anyOf": [{"$ref": "#/components/schemas/JWTResponse"}, {"type": "null"}], "title": "Response Login V1 Login Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/{strategy}/auth": {"post": {"tags": ["auth"], "summary": "Authorize", "description": "Callback authorization endpoint used for OAuth providers after authenticating on the provider's login screen.\n\nRaises:\n    HTTPException: If authentication fails, or strategy is invalid.", "operationId": "authorize_v1__strategy__auth_post", "parameters": [{"name": "strategy", "in": "path", "required": true, "schema": {"type": "string", "title": "Strategy Name", "description": "Name of strategy in question"}, "description": "Name of strategy in question"}, {"name": "code", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Code", "description": "OAuth Code"}, "description": "OAuth Code"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JWTResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/logout": {"get": {"tags": ["auth"], "summary": "Logout", "description": "Logs out the current user, adding the given JWT token to the blacklist.", "operationId": "logout_v1_logout_get", "parameters": [{"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}, {"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Logout"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/tool/auth": {"get": {"tags": ["auth"], "summary": "<PERSON><PERSON>", "description": "Endpoint for Tool Authentication. Note: The flow is different from\nthe regular login OAuth flow, the backend initiates it and redirects to the frontend\nafter completion.\n\nIf completed, a ToolAuth is stored in the DB containing the access token for the tool.\n\nReturns:\n    RedirectResponse: A redirect pointing to the frontend, contains an error query parameter if\n        an unexpected error happens during the authentication.\n\nRaises:\n    HTTPException: If no redirect_uri set.", "operationId": "tool_auth_v1_tool_auth_get", "parameters": [{"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/tool/auth/{tool_id}": {"delete": {"tags": ["auth"], "summary": "Delete Tool <PERSON>", "description": "Endpoint to delete Tool Authentication.\n\nIf completed, the corresponding ToolAuth for the requesting user is removed from the DB.\n\nRaises:\n    HTTPException: If there was an error deleting the tool auth.", "operationId": "delete_tool_auth_v1_tool_auth__tool_id__delete", "parameters": [{"name": "tool_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Tool ID", "description": "Tool ID for tool in question"}, "description": "Tool ID for tool in question"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteToolAuth"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/chat-stream": {"post": {"tags": ["chat"], "summary": "Chat Stream", "description": "Stream chat endpoint to handle user messages and return chatbot responses.", "operationId": "chat_stream_v1_chat_stream_post", "parameters": [{"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}, {"name": "deployment-name", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Deployment Name", "description": "Name of the Deployment to use for the request"}, "description": "Name of the Deployment to use for the request"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CohereChatRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ChatResponseEvent"}, "title": "Response Chat Stream V1 Chat Stream Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/chat-stream/regenerate": {"post": {"tags": ["chat"], "summary": "Regenerate Chat Stream", "description": "Endpoint to regenerate stream chat response for the last user message.", "operationId": "regenerate_chat_stream_v1_chat_stream_regenerate_post", "parameters": [{"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}, {"name": "deployment-name", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Deployment Name", "description": "Name of the Deployment to use for the request"}, "description": "Name of the Deployment to use for the request"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CohereChatRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/chat": {"post": {"tags": ["chat"], "summary": "Cha<PERSON>", "description": "Chat endpoint to handle user messages and return chatbot responses.", "operationId": "chat_v1_chat_post", "parameters": [{"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}, {"name": "deployment-name", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Deployment Name", "description": "Name of the Deployment to use for the request"}, "description": "Name of the Deployment to use for the request"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CohereChatRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NonStreamedChatResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/users": {"post": {"tags": ["user"], "summary": "Create User", "description": "Create a new user.", "operationId": "create_user_v1_users_post", "parameters": [{"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/backend__schemas__user__CreateUser"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/backend__schemas__user__User"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["user"], "summary": "List Users", "description": "List all users.", "operationId": "list_users_v1_users_get", "parameters": [{"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "title": "Pagination Offset", "description": "Offset for where request should start returning records from", "default": 0}, "description": "Offset for where request should start returning records from"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "title": "Pagination Limit", "description": "Maximum number of records to return per request", "default": 100}, "description": "Maximum number of records to return per request"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}, {"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/backend__schemas__user__User"}, "title": "Response List Users V1 Users Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/users/{user_id}": {"get": {"tags": ["user"], "summary": "Get User", "description": "Get a user by ID.\n\nRaises:\n    HTTPException: If the user with the given ID is not found.", "operationId": "get_user_v1_users__user_id__get", "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string", "title": "User ID", "description": "User ID for the user in question"}, "description": "User ID for the user in question"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}, {"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/backend__schemas__user__User"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["user"], "summary": "Update User", "description": "Update a user by ID.\n\nRaises:\n    HTTPException: If the user with the given ID is not found.", "operationId": "update_user_v1_users__user_id__put", "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string", "title": "User ID", "description": "User ID for the user in question"}, "description": "User ID for the user in question"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}, {"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/backend__schemas__user__UpdateUser"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/backend__schemas__user__User"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["user"], "summary": "Delete User", "description": "Delete a user by ID.\n\nRaises:\n    HTTPException: If the user with the given ID is not found.", "operationId": "delete_user_v1_users__user_id__delete", "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string", "title": "User ID", "description": "User ID for the user in question"}, "description": "User ID for the user in question"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}, {"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteUser"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/conversations/{conversation_id}": {"get": {"tags": ["conversation"], "summary": "Get Conversation", "description": "Get a conversation by ID.\n\nRaises:\n    HTTPException: If the conversation with the given ID is not found.", "operationId": "get_conversation_v1_conversations__conversation_id__get", "parameters": [{"name": "conversation_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Conversation ID", "description": "Conversation ID for conversation in question"}, "description": "Conversation ID for conversation in question"}, {"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConversationPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["conversation"], "summary": "Update Conversation", "description": "Update a conversation by <PERSON>.\n\nRaises:\n    HTTPException: If the conversation with the given ID is not found.", "operationId": "update_conversation_v1_conversations__conversation_id__put", "parameters": [{"name": "conversation_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Conversation ID", "description": "Conversation ID for conversation in question"}, "description": "Conversation ID for conversation in question"}, {"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateConversationRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConversationPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["conversation"], "summary": "Delete Conversation", "description": "Delete a conversation by <PERSON>.\n\nRaises:\n    HTTPException: If the conversation with the given ID is not found.", "operationId": "delete_conversation_v1_conversations__conversation_id__delete", "parameters": [{"name": "conversation_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Conversation ID", "description": "Conversation ID for conversation in question"}, "description": "Conversation ID for conversation in question"}, {"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteConversationResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/conversations": {"get": {"tags": ["conversation"], "summary": "List Conversations", "description": "List all conversations.", "operationId": "list_conversations_v1_conversations_get", "parameters": [{"name": "order_by", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON> By", "description": "Field to sorts results by"}, "description": "Field to sorts results by"}, {"name": "agent_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Agent ID", "description": "Agent ID to filter results by"}, "description": "Agent ID to filter results by"}, {"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "title": "Pagination Offset", "description": "Offset for where request should start returning records from", "default": 0}, "description": "Offset for where request should start returning records from"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "title": "Pagination Limit", "description": "Maximum number of records to return per request", "default": 100}, "description": "Maximum number of records to return per request"}, {"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ConversationWithoutMessages"}, "title": "Response List Conversations V1 Conversations Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/conversations/{conversation_id}/toggle-pin": {"put": {"tags": ["conversation"], "summary": "Toggle Conversation Pin", "description": "Toggle whether a conversation is pinned or not", "operationId": "toggle_conversation_pin_v1_conversations__conversation_id__toggle_pin_put", "parameters": [{"name": "conversation_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Conversation ID", "description": "Conversation ID for conversation in question"}, "description": "Conversation ID for conversation in question"}, {"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ToggleConversationPinRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConversationWithoutMessages"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/conversations:search": {"get": {"tags": ["conversation"], "summary": "Search Conversations", "description": "Search conversations by title.", "operationId": "search_conversations_v1_conversations_search_get", "parameters": [{"name": "query", "in": "query", "required": true, "schema": {"type": "string", "title": "Query", "description": "Query string to search for in a conversation title"}, "description": "Query string to search for in a conversation title"}, {"name": "order_by", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON> By", "description": "Field to sorts results by"}, "description": "Field to sorts results by"}, {"name": "agent_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Agent ID", "description": "Agent ID to filter results by"}, "description": "Agent ID to filter results by"}, {"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "title": "Pagination Offset", "description": "Offset for where request should start returning records from", "default": 0}, "description": "Offset for where request should start returning records from"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "title": "Pagination Limit", "description": "Maximum number of records to return per request", "default": 100}, "description": "Maximum number of records to return per request"}, {"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ConversationWithoutMessages"}, "title": "Response Search Conversations V1 Conversations Search Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/conversations/batch_upload_file": {"post": {"tags": ["conversation"], "summary": "Batch Upload File", "description": "Uploads and creates a batch of File object.\nIf no conversation_id is provided, a new Conversation is created as well.\n\nRaises:\n    HTTPException: If the conversation with the given ID is not found. Status code 404.\n    HTTPException: If the file wasn't uploaded correctly. Status code 500.", "operationId": "batch_upload_file_v1_conversations_batch_upload_file_post", "parameters": [{"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_batch_upload_file_v1_conversations_batch_upload_file_post"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UploadConversationFileResponse"}, "title": "Response Batch Upload File V1 Conversations Batch Upload File Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/conversations/{conversation_id}/files": {"get": {"tags": ["conversation"], "summary": "List Files", "description": "List all files from a conversation. Important - no pagination support yet.\n\nRaises:\n    HTTPException: If the conversation with the given ID is not found.", "operationId": "list_files_v1_conversations__conversation_id__files_get", "parameters": [{"name": "conversation_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Conversation ID", "description": "Conversation ID for conversation in question"}, "description": "Conversation ID for conversation in question"}, {"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ListConversationFile"}, "title": "Response List Files V1 Conversations  Conversation Id  Files Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/conversations/{conversation_id}/files/{file_id}": {"get": {"tags": ["conversation"], "summary": "Get File", "description": "Get a conversation file by ID.\n\nRaises:\n    HTTPException: If the conversation or file with the given ID is not found, or if the file does not belong to the conversation.", "operationId": "get_file_v1_conversations__conversation_id__files__file_id__get", "parameters": [{"name": "conversation_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Conversation ID", "description": "Conversation ID for conversation in question"}, "description": "Conversation ID for conversation in question"}, {"name": "file_id", "in": "path", "required": true, "schema": {"type": "string", "title": "File ID", "description": "File ID for file in question"}, "description": "File ID for file in question"}, {"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FileMetadata"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["conversation"], "summary": "Delete File", "description": "Delete a file by ID.\n\nRaises:\n    HTTPException: If the conversation with the given ID is not found.", "operationId": "delete_file_v1_conversations__conversation_id__files__file_id__delete", "parameters": [{"name": "conversation_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Conversation ID", "description": "Conversation ID for conversation in question"}, "description": "Conversation ID for conversation in question"}, {"name": "file_id", "in": "path", "required": true, "schema": {"type": "string", "title": "File ID", "description": "File ID for file in question"}, "description": "File ID for file in question"}, {"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteConversationFileResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/conversations/{conversation_id}/generate-title": {"post": {"tags": ["conversation"], "summary": "Generate Title", "description": "Generate a title for a conversation and update the conversation with the generated title.\n\nRaises:\n    HTTPException: If the conversation with the given ID is not found.", "operationId": "generate_title_v1_conversations__conversation_id__generate_title_post", "parameters": [{"name": "conversation_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Conversation ID", "description": "Conversation ID for conversation in question"}, "description": "Conversation ID for conversation in question"}, {"name": "model", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Model", "description": "Model to filter results by", "default": "command-r"}, "description": "Model to filter results by"}, {"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenerateTitleResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/conversations/{conversation_id}/synthesize/{message_id}": {"get": {"tags": ["conversation"], "summary": "Synthesize Message", "description": "Generate a synthesized audio for a specific message in a conversation.\n\nRaises:\n    HTTPException: If the message with the given ID is not found or synthesis fails.", "operationId": "synthesize_message_v1_conversations__conversation_id__synthesize__message_id__get", "parameters": [{"name": "conversation_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Conversation ID", "description": "Conversation ID for conversation in question"}, "description": "Conversation ID for conversation in question"}, {"name": "message_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Message ID", "description": "Message ID for message in question"}, "description": "Message ID for message in question"}, {"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/tools": {"get": {"tags": ["tool"], "summary": "List Tools", "description": "List all available tools.", "operationId": "list_tools_v1_tools_get", "parameters": [{"name": "agent_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Agent ID", "description": "Agent ID to filter results by"}, "description": "Agent ID to filter results by"}, {"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ToolDefinition"}, "title": "Response List Tools V1 Tools Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/deployments": {"post": {"tags": ["deployment"], "summary": "Create Deployment", "description": "Create a new deployment.", "operationId": "create_deployment_v1_deployments_post", "parameters": [{"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeploymentCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeploymentDefinition"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["deployment"], "summary": "List Deployments", "description": "List all available deployments and their models.", "operationId": "list_deployments_v1_deployments_get", "parameters": [{"name": "all", "in": "query", "required": false, "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "All", "description": "Include all deployments, regardless of availability.", "default": false}, "description": "Include all deployments, regardless of availability."}, {"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeploymentDefinition"}, "title": "Response List Deployments V1 Deployments Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/deployments/{deployment_id}": {"put": {"tags": ["deployment"], "summary": "Update Deployment", "description": "Update a deployment.\n\nRaises:\n    HTTPException: If deployment not found.", "operationId": "update_deployment_v1_deployments__deployment_id__put", "parameters": [{"name": "deployment_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Deployment ID", "description": "Deployment ID for deployment in question"}, "description": "Deployment ID for deployment in question"}, {"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeploymentUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeploymentDefinition"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["deployment"], "summary": "Get Deployment", "description": "Get a deployment by ID.", "operationId": "get_deployment_v1_deployments__deployment_id__get", "parameters": [{"name": "deployment_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Deployment ID", "description": "Deployment ID for deployment in question"}, "description": "Deployment ID for deployment in question"}, {"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeploymentDefinition"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["deployment"], "summary": "Delete Deployment", "description": "Delete a deployment by ID.\n\nRaises:\n    HTTPException: If the deployment with the given ID is not found.", "operationId": "delete_deployment_v1_deployments__deployment_id__delete", "parameters": [{"name": "deployment_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Deployment ID", "description": "Deployment ID for deployment in question"}, "description": "Deployment ID for deployment in question"}, {"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteDeployment"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/deployments/{deployment_id}/update_config": {"post": {"tags": ["deployment"], "summary": "Update Config", "description": "Set environment variables for the deployment.", "operationId": "update_config_v1_deployments__deployment_id__update_config_post", "parameters": [{"name": "deployment_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Deployment ID", "description": "Deployment ID for deployment in question"}, "description": "Deployment ID for deployment in question"}, {"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateDeploymentEnv"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeploymentDefinition"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/experimental_features/": {"get": {"tags": ["experimental_features"], "summary": "List Experimental Features", "description": "List all experimental features and if they are enabled", "operationId": "list_experimental_features_v1_experimental_features__get", "parameters": [{"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "boolean"}, "title": "Response List Experimental Features V1 Experimental Features  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/agents": {"post": {"tags": ["agent"], "summary": "Create Agent", "description": "Create an agent.\n\nRaises:\n    HTTPException: If the agent creation fails.", "operationId": "create_agent_v1_agents_post", "parameters": [{"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateAgentRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AgentPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["agent"], "summary": "List Agents", "description": "List all agents.", "operationId": "list_agents_v1_agents_get", "parameters": [{"name": "visibility", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/AgentVisibility", "title": "Visibility", "description": "Agent visibility", "default": "all"}, "description": "Agent visibility"}, {"name": "org_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Organization ID to filter results by"}, "description": "Organization ID to filter results by"}, {"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "title": "Pagination Offset", "description": "Offset for where request should start returning records from", "default": 0}, "description": "Offset for where request should start returning records from"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "title": "Pagination Limit", "description": "Maximum number of records to return per request", "default": 100}, "description": "Maximum number of records to return per request"}, {"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AgentPublic"}, "title": "Response List Agents V1 Agents Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/agents/{agent_id}": {"get": {"tags": ["agent"], "summary": "Get Agent By Id", "description": "Return an agent by ID.\n\nRaises:\n    HTTPException: If the agent with the given ID is not found.", "operationId": "get_agent_by_id_v1_agents__agent_id__get", "parameters": [{"name": "agent_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Agent ID", "description": "Agent ID for agent in question"}, "description": "Agent ID for agent in question"}, {"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AgentPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["agent"], "summary": "Update Agent", "description": "Update an agent by ID.\n\nRaises:\n    HTTPException: If the agent with the given ID is not found.", "operationId": "update_agent_v1_agents__agent_id__put", "parameters": [{"name": "agent_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Agent ID", "description": "Agent ID for agent in question"}, "description": "Agent ID for agent in question"}, {"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateAgentRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AgentPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["agent"], "summary": "Delete Agent", "description": "Delete an agent by ID.\n\nRaises:\n    HTTPException: If the agent with the given ID is not found.", "operationId": "delete_agent_v1_agents__agent_id__delete", "parameters": [{"name": "agent_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Agent ID", "description": "Agent ID for agent in question"}, "description": "Agent ID for agent in question"}, {"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteAgent"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/agents/{agent_id}/deployments": {"get": {"tags": ["agent"], "summary": "Get Agent Deployment", "description": "Get the deployment for an agent\n\nRaises:\n    HTTPException: If the agent with the given ID is not found.", "operationId": "get_agent_deployment_v1_agents__agent_id__deployments_get", "parameters": [{"name": "agent_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Agent ID", "description": "Agent ID for agent in question"}, "description": "Agent ID for agent in question"}, {"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeploymentDefinition"}, "title": "Response Get Agent Deployment V1 Agents  Agent Id  Deployments Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/agents/{agent_id}/tool-metadata": {"get": {"tags": ["agent"], "summary": "List Agent <PERSON><PERSON>", "description": "List all agent tool metadata by agent ID.\n\nRaises:\n    HTTPException: If the agent tool metadata retrieval fails.", "operationId": "list_agent_tool_metadata_v1_agents__agent_id__tool_metadata_get", "parameters": [{"name": "agent_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Agent ID", "description": "Agent ID for agent in question"}, "description": "Agent ID for agent in question"}, {"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AgentToolMetadataPublic"}, "title": "Response List Agent <PERSON><PERSON> Metadata V1 Agents  Agent Id  Tool Metadata Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["agent"], "summary": "Create Agent <PERSON><PERSON>", "description": "Create an agent tool metadata.\n\nRaises:\n    HTTPException: If the agent tool metadata creation fails.", "operationId": "create_agent_tool_metadata_v1_agents__agent_id__tool_metadata_post", "parameters": [{"name": "agent_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Agent ID", "description": "Agent ID for agent in question"}, "description": "Agent ID for agent in question"}, {"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateAgentToolMetadataRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AgentToolMetadataPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/agents/{agent_id}/tool-metadata/{agent_tool_metadata_id}": {"put": {"tags": ["agent"], "summary": "Update Agent <PERSON><PERSON>", "description": "Update an agent tool metadata by ID.\n\nRaises:\n    HTTPException: If the agent tool metadata with the given ID is not found.\n    HTTPException: If the agent tool metadata update fails.", "operationId": "update_agent_tool_metadata_v1_agents__agent_id__tool_metadata__agent_tool_metadata_id__put", "parameters": [{"name": "agent_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Agent ID", "description": "Agent ID for agent in question"}, "description": "Agent ID for agent in question"}, {"name": "agent_tool_metadata_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Agent <PERSON><PERSON>", "description": "Agent Tool Metadata ID for tool metadata in question"}, "description": "Agent Tool Metadata ID for tool metadata in question"}, {"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateAgentToolMetadataRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AgentToolMetadata"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["agent"], "summary": "Delete Agent <PERSON><PERSON>", "description": "Delete an agent tool metadata by ID.\n\nRaises:\n    HTTPException: If the agent tool metadata with the given ID is not found.\n    HTTPException: If the agent tool metadata deletion fails.", "operationId": "delete_agent_tool_metadata_v1_agents__agent_id__tool_metadata__agent_tool_metadata_id__delete", "parameters": [{"name": "agent_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Agent ID", "description": "Agent ID for agent in question"}, "description": "Agent ID for agent in question"}, {"name": "agent_tool_metadata_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Agent <PERSON><PERSON>", "description": "Agent Tool Metadata ID for tool metadata in question"}, "description": "Agent Tool Metadata ID for tool metadata in question"}, {"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteAgentToolMetadata"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/agents/batch_upload_file": {"post": {"tags": ["agent"], "summary": "Batch Upload File", "description": "Upload a batch of files", "operationId": "batch_upload_file_v1_agents_batch_upload_file_post", "parameters": [{"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_batch_upload_file_v1_agents_batch_upload_file_post"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UploadAgentFileResponse"}, "title": "Response Batch Upload File V1 Agents Batch Upload File Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/agents/{agent_id}/files/{file_id}": {"get": {"tags": ["agent"], "summary": "Get Agent File", "description": "Get an agent file by ID.\n\nRaises:\n    HTTPException: If the agent or file with the given ID is not found, or if the file does not belong to the agent.", "operationId": "get_agent_file_v1_agents__agent_id__files__file_id__get", "parameters": [{"name": "agent_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Agent ID", "description": "Agent ID for agent in question"}, "description": "Agent ID for agent in question"}, {"name": "file_id", "in": "path", "required": true, "schema": {"type": "string", "title": "File ID", "description": "File ID for file in question"}, "description": "File ID for file in question"}, {"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FileMetadata"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["agent"], "summary": "Delete Agent File", "description": "Delete an agent file by ID.\n\nRaises:\n    HTTPException: If the agent with the given ID is not found.", "operationId": "delete_agent_file_v1_agents__agent_id__files__file_id__delete", "parameters": [{"name": "agent_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Agent ID", "description": "Agent ID for agent in question"}, "description": "Agent ID for agent in question"}, {"name": "file_id", "in": "path", "required": true, "schema": {"type": "string", "title": "File ID", "description": "File ID for file in question"}, "description": "File ID for file in question"}, {"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteAgentFileResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/snapshots": {"post": {"tags": ["snapshot"], "summary": "Create Snapshot", "description": "Create a new snapshot and snapshot link to share the conversation.", "operationId": "create_snapshot_v1_snapshots_post", "parameters": [{"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateSnapshotRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateSnapshotResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["snapshot"], "summary": "List Snapshots", "description": "List all snapshots.", "operationId": "list_snapshots_v1_snapshots_get", "parameters": [{"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SnapshotWithLinks"}, "title": "Response List Snapshots V1 Snapshots Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/snapshots/link/{link_id}": {"get": {"tags": ["snapshot"], "summary": "Get Snapshot", "description": "Get a snapshot by link ID.", "operationId": "get_snapshot_v1_snapshots_link__link_id__get", "parameters": [{"name": "link_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Link ID", "description": "Link ID for the snapshot link in question"}, "description": "Link ID for the snapshot link in question"}, {"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SnapshotPublic"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["snapshot"], "summary": "Delete Snapshot Link", "description": "Delete a snapshot link by ID.", "operationId": "delete_snapshot_link_v1_snapshots_link__link_id__delete", "parameters": [{"name": "link_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Link ID", "description": "Link ID for the snapshot link in question"}, "description": "Link ID for the snapshot link in question"}, {"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteSnapshotLinkResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/snapshots/{snapshot_id}": {"delete": {"tags": ["snapshot"], "summary": "Delete Snapshot", "description": "Delete a snapshot by ID.", "operationId": "delete_snapshot_v1_snapshots__snapshot_id__delete", "parameters": [{"name": "snapshot_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Snapshot ID", "description": "Snapshot ID for the snapshot in question"}, "description": "Snapshot ID for the snapshot in question"}, {"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteSnapshotResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/organizations": {"post": {"tags": ["organization"], "summary": "Create Organization", "description": "Create a new organization.", "operationId": "create_organization_v1_organizations_post", "parameters": [{"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateOrganization"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Organization"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["organization"], "summary": "List Organizations", "description": "List all available organizations.", "operationId": "list_organizations_v1_organizations_get", "parameters": [{"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Organization"}, "title": "Response List Organizations V1 Organizations Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/organizations/{org_id}": {"put": {"tags": ["organization"], "summary": "Update Organization", "description": "Update organization by ID.", "operationId": "update_organization_v1_organizations__org_id__put", "parameters": [{"name": "org_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Organization ID", "description": "Organization ID for the organization in question"}, "description": "Organization ID for the organization in question"}, {"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateOrganization"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Organization"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["organization"], "summary": "Get Organization", "description": "Get a organization by ID.", "operationId": "get_organization_v1_organizations__org_id__get", "parameters": [{"name": "org_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Organization ID", "description": "Organization ID for the organization in question"}, "description": "Organization ID for the organization in question"}, {"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Organization"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["organization"], "summary": "Delete Organization", "description": "Delete a organization by ID.", "operationId": "delete_organization_v1_organizations__org_id__delete", "parameters": [{"name": "org_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Organization ID", "description": "Organization ID for the organization in question"}, "description": "Organization ID for the organization in question"}, {"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteOrganization"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/organizations/{org_id}/users": {"get": {"tags": ["organization"], "summary": "Get Organization Users", "description": "Get organization users by ID.", "operationId": "get_organization_users_v1_organizations__org_id__users_get", "parameters": [{"name": "org_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Organization ID", "description": "Organization ID for the organization in question"}, "description": "Organization ID for the organization in question"}, {"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/backend__schemas__user__User"}, "title": "Response Get Organization Users V1 Organizations  Org Id  Users Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/models": {"post": {"tags": ["model"], "summary": "Create Model", "description": "Create a new model.", "operationId": "create_model_v1_models_post", "parameters": [{"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ModelCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Model"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["model"], "summary": "List Models", "description": "List all available models", "operationId": "list_models_v1_models_get", "parameters": [{"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "title": "Pagination Offset", "description": "Offset for where request should start returning records from", "default": 0}, "description": "Offset for where request should start returning records from"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "title": "Pagination Limit", "description": "Maximum number of records to return per request", "default": 100}, "description": "Maximum number of records to return per request"}, {"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Model"}, "title": "Response List Models V1 Models Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/models/{model_id}": {"put": {"tags": ["model"], "summary": "Update Model", "description": "Update a model by ID.\n\nRaises:\n    HTTPException: If the model with the given ID is not found.", "operationId": "update_model_v1_models__model_id__put", "parameters": [{"name": "model_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Model ID", "description": "Model ID for the model in question"}, "description": "Model ID for the model in question"}, {"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ModelUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Model"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["model"], "summary": "Get Model", "description": "Get a model by ID.", "operationId": "get_model_v1_models__model_id__get", "parameters": [{"name": "model_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Model ID", "description": "Model ID for the model in question"}, "description": "Model ID for the model in question"}, {"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Model"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["model"], "summary": "Delete Model", "description": "Delete a model by ID.\n\nRaises:\n    HTTPException: If the model with the given ID is not found.", "operationId": "delete_model_v1_models__model_id__delete", "parameters": [{"name": "model_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Model ID", "description": "Model ID for the model in question"}, "description": "Model ID for the model in question"}, {"name": "authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization", "description": "Authorization header containing Bear<PERSON> token"}, "description": "Authorization header containing Bear<PERSON> token"}, {"name": "Organization-Id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Unique Identifier for the Organization making the request"}, "description": "Unique Identifier for the Organization making the request"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteModel"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/scim/v2/Users": {"get": {"tags": ["scim"], "summary": "Get Users", "description": "Return users", "operationId": "get_users_scim_v2_Users_get", "security": [{"HTTPBasic": []}], "parameters": [{"name": "start_index", "in": "query", "required": false, "schema": {"type": "integer", "title": "Start Index", "description": "Start Index for request", "default": 1}, "description": "Start Index for request"}, {"name": "count", "in": "query", "required": false, "schema": {"type": "integer", "title": "Count", "description": "Maximum number of records to return per request", "default": 100}, "description": "Maximum number of records to return per request"}, {"name": "filter", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Filter", "description": "Filter to use when filtering response"}, "description": "Filter to use when filtering response"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListUserResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["scim"], "summary": "Create User", "description": "Create a new user", "operationId": "create_user_scim_v2_Users_post", "security": [{"HTTPBasic": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/backend__schemas__scim__CreateUser"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/scim/v2/Users/<USER>": {"get": {"tags": ["scim"], "summary": "Get User", "description": "Get user by User ID", "operationId": "get_user_scim_v2_Users__user_id__get", "security": [{"HTTPBasic": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string", "title": "User ID", "description": "User ID for the user in question"}, "description": "User ID for the user in question"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["scim"], "summary": "Update User", "description": "Update a user", "operationId": "update_user_scim_v2_Users__user_id__put", "security": [{"HTTPBasic": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string", "title": "User ID", "description": "User ID for the user in question"}, "description": "User ID for the user in question"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/backend__schemas__scim__UpdateUser"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "patch": {"tags": ["scim"], "summary": "Patch User", "description": "Patch a user", "operationId": "patch_user_scim_v2_Users__user_id__patch", "security": [{"HTTPBasic": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string", "title": "User ID", "description": "User ID for the user in question"}, "description": "User ID for the user in question"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PatchUser"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/scim/v2/Groups": {"get": {"tags": ["scim"], "summary": "Get Groups", "description": "Return Groups", "operationId": "get_groups_scim_v2_Groups_get", "security": [{"HTTPBasic": []}], "parameters": [{"name": "start_index", "in": "query", "required": false, "schema": {"type": "integer", "title": "Start Index", "description": "Start Index for request", "default": 1}, "description": "Start Index for request"}, {"name": "count", "in": "query", "required": false, "schema": {"type": "integer", "title": "Count", "description": "Maximum number of records to return per request", "default": 100}, "description": "Maximum number of records to return per request"}, {"name": "filter", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Filter", "description": "Filter to use when filtering response"}, "description": "Filter to use when filtering response"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListGroupResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["scim"], "summary": "Create Group", "description": "Create a group", "operationId": "create_group_scim_v2_Groups_post", "security": [{"HTTPBasic": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateGroup"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/scim/v2/Groups/{group_id}": {"get": {"tags": ["scim"], "summary": "Get Group", "description": "Get group by group ID", "operationId": "get_group_scim_v2_Groups__group_id__get", "security": [{"HTTPBasic": []}], "parameters": [{"name": "group_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Group ID", "description": "Group ID for the group in question"}, "description": "Group ID for the group in question"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "patch": {"tags": ["scim"], "summary": "Patch Group", "description": "Patch a group", "operationId": "patch_group_scim_v2_Groups__group_id__patch", "security": [{"HTTPBasic": []}], "parameters": [{"name": "group_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Group ID", "description": "Group ID for the group in question"}, "description": "Group ID for the group in question"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PatchGroup"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["scim"], "summary": "Delete Group", "description": "Delete a group", "operationId": "delete_group_scim_v2_Groups__group_id__delete", "security": [{"HTTPBasic": []}], "parameters": [{"name": "group_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Group ID", "description": "Group ID for the group in question"}, "description": "Group ID for the group in question"}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/health": {"get": {"summary": "Health", "description": "Health check for backend APIs", "operationId": "health_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}}, "components": {"schemas": {"AgentPublic": {"properties": {"user_id": {"type": "string", "title": "User ID", "description": "User ID for the Agent"}, "id": {"type": "string", "title": "ID", "description": "Agent ID"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At Timestamp", "description": "When the agent was created"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At Timestamp", "description": "When the agent was updated"}, "name": {"type": "string", "title": "Name", "description": "Name of the Agent"}, "version": {"type": "integer", "title": "Version", "description": "Version of the Agent"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Agent Description"}, "preamble": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Preamble", "description": "The preamble for the Agent"}, "temperature": {"type": "number", "title": "Temperature", "description": "The temperature for the Agent"}, "tools": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Tools", "description": "List of tools for the Agent"}, "tools_metadata": {"anyOf": [{"items": {"$ref": "#/components/schemas/AgentToolMetadataPublic"}, "type": "array"}, {"type": "null"}], "title": "<PERSON><PERSON>", "description": "List of tool metadata for the Agent"}, "deployment": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Deployment", "description": "Deployment for the Agent"}, "model": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Model", "description": "Model for the Agent"}, "is_private": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Private", "description": "If the Agent is private"}}, "type": "object", "required": ["user_id", "id", "created_at", "updated_at", "name", "version", "temperature"], "title": "AgentPublic", "description": "Public agent schema"}, "AgentToolMetadata": {"properties": {"id": {"type": "string", "title": "ID", "description": "Agent tool metadata ID"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At Timestamp", "description": "When the agent tool metadata was created"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At Timestamp", "description": "When the agent tool metadata was updated"}, "user_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "User ID", "description": "User ID for the agent tool metadata"}, "agent_id": {"type": "string", "title": "Agent ID", "description": "Agent ID for the agent tool metadata"}, "tool_name": {"type": "string", "title": "Tool Name", "description": "Tool Name for the agent tool metadata"}, "artifacts": {"items": {"type": "object"}, "type": "array", "title": "Artifacts", "description": "Artifacts for the agent tool metadata"}}, "type": "object", "required": ["id", "created_at", "updated_at", "agent_id", "tool_name", "artifacts"], "title": "AgentToolMetadata", "description": "Agent tool metadata schema"}, "AgentToolMetadataPublic": {"properties": {"id": {"type": "string", "title": "ID", "description": "Agent tool metadata ID"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At Timestamp", "description": "When the agent tool metadata was created"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At Timestamp", "description": "When the agent tool metadata was updated"}, "agent_id": {"type": "string", "title": "Agent ID", "description": "Agent ID for the agent tool metadata"}, "tool_name": {"type": "string", "title": "Tool Name", "description": "Tool Name for the agent tool metadata"}, "artifacts": {"items": {"type": "object"}, "type": "array", "title": "Artifacts", "description": "Artifacts for the agent tool metadata"}}, "type": "object", "required": ["id", "created_at", "updated_at", "agent_id", "tool_name", "artifacts"], "title": "AgentToolMetadataPublic", "description": "Public agent tool metadata schema"}, "AgentVisibility": {"type": "string", "enum": ["private", "public", "all"], "title": "AgentVisibility", "description": "Supported values for Agent Visibility"}, "Body_batch_upload_file_v1_agents_batch_upload_file_post": {"properties": {"files": {"items": {"type": "string", "format": "binary"}, "type": "array", "title": "Files"}}, "type": "object", "required": ["files"], "title": "Body_batch_upload_file_v1_agents_batch_upload_file_post"}, "Body_batch_upload_file_v1_conversations_batch_upload_file_post": {"properties": {"conversation_id": {"type": "string", "title": "Conversation Id"}, "files": {"items": {"type": "string", "format": "binary"}, "type": "array", "title": "Files"}}, "type": "object", "required": ["files"], "title": "Body_batch_upload_file_v1_conversations_batch_upload_file_post"}, "ChatMessage": {"properties": {"role": {"$ref": "#/components/schemas/ChatRole", "title": "Role"}, "message": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Message", "description": "Contents of the chat message."}, "tool_plan": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Tool Plan", "description": "Contents of the tool plan."}, "tool_results": {"anyOf": [{"items": {"type": "object"}, "type": "array"}, {"type": "null"}], "title": "Tool Results", "description": "Results from the tool call."}, "tool_calls": {"anyOf": [{"items": {"type": "object"}, "type": "array"}, {"type": "null"}], "title": "Tool Calls", "description": "List of tool calls generated for custom tools"}}, "type": "object", "required": ["role"], "title": "ChatMessage", "description": "A list of previous messages between the user and the model, meant to give the mode\nconversational context for responding to the user's message."}, "ChatResponseEvent": {"properties": {"event": {"$ref": "#/components/schemas/StreamEvent", "title": "Event", "description": "Type of stream event"}, "data": {"anyOf": [{"$ref": "#/components/schemas/StreamStart"}, {"$ref": "#/components/schemas/StreamTextGeneration"}, {"$ref": "#/components/schemas/StreamCitationGeneration"}, {"$ref": "#/components/schemas/StreamQueryGeneration"}, {"$ref": "#/components/schemas/StreamSearchResults"}, {"$ref": "#/components/schemas/StreamEnd"}, {"$ref": "#/components/schemas/StreamToolInput"}, {"$ref": "#/components/schemas/StreamToolResult"}, {"$ref": "#/components/schemas/StreamSearchQueriesGeneration"}, {"$ref": "#/components/schemas/StreamToolCallsGeneration"}, {"$ref": "#/components/schemas/StreamToolCallsChunk"}, {"$ref": "#/components/schemas/NonStreamedChatResponse"}], "title": "Data", "description": "Data returned from chat response of a given event type"}}, "type": "object", "required": ["event", "data"], "title": "ChatResponseEvent", "description": "Chat Response Event"}, "ChatRole": {"type": "string", "enum": ["CHATBOT", "USER", "SYSTEM", "TOOL"], "title": "ChatRole", "description": "One of CHATBOT|USER|SYSTEM to identify who the message is coming from."}, "Citation": {"properties": {"text": {"type": "string", "title": "Text", "description": "Citation text"}, "start": {"type": "integer", "title": "Start", "description": "Start position for the citation"}, "end": {"type": "integer", "title": "End", "description": "End position for the citation"}, "document_ids": {"items": {"type": "string"}, "type": "array", "title": "Document IDs", "description": "Documents used for the citation"}}, "type": "object", "required": ["text", "start", "end", "document_ids"], "title": "Citation", "description": "<PERSON><PERSON><PERSON> for a citation"}, "CohereChatPromptTruncation": {"type": "string", "enum": ["OFF", "AUTO_PRESERVE_ORDER"], "title": "CohereChatPromptTruncation", "description": "Dictates how the prompt will be constructed. Defaults to \"AUTO_PRESERVE_ORDER\"."}, "CohereChatRequest": {"properties": {"message": {"type": "string", "title": "Message", "description": "The message to send to the chatbot"}, "chat_history": {"anyOf": [{"items": {"$ref": "#/components/schemas/ChatMessage"}, "type": "array"}, {"type": "null"}], "title": "Chat History", "description": "A list of entries used to construct the conversation. If provided, these messages will be used to build the prompt and the conversation_id will be ignored so no data will be stored to maintain state."}, "conversation_id": {"type": "string", "title": "Conversation ID", "description": "To store a conversation then create a conversation id and use it for every related request"}, "tools": {"anyOf": [{"items": {"$ref": "#/components/schemas/Tool"}, "type": "array"}, {"type": "null"}], "title": "Tools", "description": "\n            List of custom or managed tools to use for the response.\n            If passing in managed tools, you only need to provide the name of the tool.\n            If passing in custom tools, you need to provide the name, description, and optionally parameter defintions of the tool.\n            Passing a mix of custom and managed tools is not supported.\n\n            Managed Tools Examples:\n            tools=[\n                {\n                    \"name\": \"Wiki Retriever - <PERSON><PERSON>hain\",\n                },\n                {\n                    \"name\": \"Calculator\",\n                }\n            ]\n\n            Custom Tools Examples:\n            tools=[\n                {\n                    \"name\": \"movie_title_generator\",\n                    \"description\": \"tool to generate a cool movie title\",\n                    \"parameter_definitions\": {\n                        \"synopsis\": {\n                            \"description\": \"short synopsis of the movie\",\n                            \"type\": \"str\",\n                            \"required\": true\n                        }\n                    }\n                },\n                {\n                    \"name\": \"random_number_generator\",\n                    \"description\": \"tool to generate a random number between min and max\",\n                    \"parameter_definitions\": {\n                        \"min\": {\n                            \"description\": \"minimum number\",\n                            \"type\": \"int\",\n                            \"required\": true\n                        },\n                        \"max\": {\n                            \"description\": \"maximum number\",\n                            \"type\": \"int\",\n                            \"required\": true\n                        }\n                    }\n                },\n                {\n                    \"name\": \"joke_generator\",\n                    \"description\": \"tool to generate a random joke\",\n                }\n            ]\n        "}, "documents": {"items": {"type": "object"}, "type": "array", "title": "Documents", "description": "Documents to use to generate grounded response with citations. Example:\n            documents=[\n                {\n                    \"id\": \"national_geographic_everest\",\n                    \"title\": \"Height of Mount Everest\",\n                    \"text\": \"The height of Mount Everest is 29,035 feet\",\n                    \"url\": \"https://education.nationalgeographic.org/resource/mount-everest/\",\n                },\n                {\n                    \"id\": \"national_geographic_mariana\",\n                    \"title\": \"Depth of the Mariana Trench\",\n                    \"text\": \"The depth of the Mariana Trench is 36,070 feet\",\n                    \"url\": \"https://www.nationalgeographic.org/activity/mariana-trench-deepest-place-earth\",\n                },\n            ]\n        "}, "model": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Model", "description": "The model to use for generating the response.", "default": "command-r-plus"}, "temperature": {"anyOf": [{"type": "number", "minimum": 0.0}, {"type": "null"}], "title": "Temperature", "description": "A non-negative float that tunes the degree of randomness in generation. Lower temperatures mean less random generations, and higher temperatures mean more random generations."}, "k": {"anyOf": [{"type": "integer", "maximum": 500.0, "minimum": 0.0}, {"type": "null"}], "title": "Top-K", "description": "Ensures only the top k most likely tokens are considered for generation at each step."}, "p": {"anyOf": [{"type": "number", "maximum": 0.99, "minimum": 0.0}, {"type": "null"}], "title": "Top-P", "description": "Ensures that only the most likely tokens, with total probability mass of p, are considered for generation at each step. If both k and p are enabled, p acts after k."}, "preamble": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Preamble", "description": "A string to override the preamble."}, "file_ids": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "File IDs", "description": "List of File IDs for PDFs used in RAG for the response."}, "search_queries_only": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Search Queries Only", "description": "When set to true a list of search queries are generated. No search will occur nor replies to the user's message.", "default": false}, "max_tokens": {"anyOf": [{"type": "integer", "minimum": 1.0}, {"type": "null"}], "title": "<PERSON>", "description": "The maximum number of tokens the model will generate as part of the response. Note: Setting a low value may result in incomplete generations."}, "seed": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Seed", "description": "If specified, the backend will make a best effort to sample tokens deterministically, such that repeated requests with the same seed and parameters should return the same result. However, determinism cannot be totally guaranteed."}, "stop_sequences": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Stop Sequences", "description": "A list of up to 5 strings that the model will use to stop generation. If the model generates a string that matches any of the strings in the list, it will stop generating tokens and return the generated text up to that point not including the stop sequence."}, "presence_penalty": {"anyOf": [{"type": "number", "maximum": 1.0, "minimum": 0.0}, {"type": "null"}], "title": "Presence Penalty", "description": "Used to reduce repetitiveness of generated tokens. Similar to frequency_penalty, except that this penalty is applied equally to all tokens that have already appeared, regardless of their exact frequencies."}, "frequency_penalty": {"anyOf": [{"type": "number", "maximum": 1.0, "minimum": 0.0}, {"type": "null"}], "title": "Frequency Penalty", "description": "Used to reduce repetitiveness of generated tokens. The higher the value, the stronger a penalty is applied to previously present tokens, proportional to how many times they have already appeared in the prompt or prior generation."}, "prompt_truncation": {"anyOf": [{"$ref": "#/components/schemas/CohereChatPromptTruncation"}, {"type": "null"}], "title": "Prompt Truncation", "description": "Dictates how the prompt will be constructed. Defaults to 'AUTO_PRESERVE_ORDER'.", "default": "AUTO_PRESERVE_ORDER"}, "tool_results": {"anyOf": [{"items": {"type": "object"}, "type": "array"}, {"type": "null"}], "title": "Tool Results", "description": "A list of results from invoking tools recommended by the model in the previous chat turn. Results are used to produce a text response and will be referenced in citations."}, "force_single_step": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Force Single Step", "description": "If set to true, the model will generate a single response in a single step. This is useful for generating a response to a single message."}, "agent_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Agent ID", "description": "The agent ID to use for the chat."}}, "type": "object", "required": ["message"], "title": "CohereChatRequest", "description": "Request shape for Cohere Python SDK Streamed Chat.\nSee: https://github.com/cohere-ai/cohere-python/blob/main/src/cohere/base_client.py#L1629"}, "ConversationFilePublic": {"properties": {"id": {"type": "string", "title": "ID", "description": "Unique identifier of the file"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At Timestamp", "description": "When file was created"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At Timestamp", "description": "When file was updated"}, "file_name": {"type": "string", "title": "File Name", "description": "Name of the file"}, "file_size": {"type": "integer", "minimum": 0.0, "title": "File Size", "description": "Size of the file in bytes", "default": 0}, "user_id": {"type": "string", "title": "User ID", "description": "Unique identifier for who created the file"}, "conversation_id": {"type": "string", "title": "Conversation ID", "description": "Unique identifier for the conversation the file is associated to"}}, "type": "object", "required": ["id", "created_at", "updated_at", "file_name", "user_id", "conversation_id"], "title": "ConversationFilePublic", "description": "Schema for a public conversation file"}, "ConversationPublic": {"properties": {"id": {"type": "string", "title": "ID", "description": "Unique identifier for the conversation"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At Timestamp", "description": "When the conversation was created"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At Timestamp", "description": "When the conversation was updated"}, "title": {"type": "string", "title": "Title", "description": "Title of the conversation"}, "messages": {"items": {"$ref": "#/components/schemas/Message"}, "type": "array", "title": "Messages", "description": "The conversation messages"}, "files": {"items": {"$ref": "#/components/schemas/ConversationFilePublic"}, "type": "array", "title": "Files", "description": "List of files for the conversation"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Description of the conversation"}, "agent_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Agent ID", "description": "Unique identifier for the agent used in the conversation"}, "is_pinned": {"type": "boolean", "title": "Is Pinned", "description": "If conversation is pinned"}, "total_file_size": {"type": "integer", "title": "Total File Size", "readOnly": true}}, "type": "object", "required": ["id", "created_at", "updated_at", "title", "messages", "files", "is_pinned", "total_file_size"], "title": "ConversationPublic", "description": "A public conversation which removes the User ID and Organization ID"}, "ConversationWithoutMessages": {"properties": {"id": {"type": "string", "title": "ID", "description": "Unique identifier for the conversation"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At Timestamp", "description": "When the conversation was created"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At Timestamp", "description": "When the conversation was updated"}, "title": {"type": "string", "title": "Title", "description": "Title of the conversation"}, "files": {"items": {"$ref": "#/components/schemas/ConversationFilePublic"}, "type": "array", "title": "Files", "description": "List of files for the conversation"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Description of the conversation"}, "agent_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Agent ID", "description": "Unique identifier for the agent used in the conversation"}, "is_pinned": {"type": "boolean", "title": "Is Pinned", "description": "If conversation is pinned"}, "total_file_size": {"type": "integer", "title": "Total File Size", "readOnly": true}}, "type": "object", "required": ["id", "created_at", "updated_at", "title", "files", "is_pinned", "total_file_size"], "title": "ConversationWithoutMessages", "description": "A public conversation without messages attached"}, "CreateAgentRequest": {"properties": {"name": {"type": "string", "title": "Name", "description": "Name of the Agent"}, "version": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Version", "description": "Version of the Agent"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Agent Description"}, "preamble": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Preamble", "description": "The preamble for the Agent"}, "temperature": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Temperature", "description": "The temperature for the Agent"}, "tools": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Tools", "description": "List of tools for the Agent"}, "tools_metadata": {"anyOf": [{"items": {"$ref": "#/components/schemas/CreateAgentToolMetadataRequest"}, "type": "array"}, {"type": "null"}], "title": "<PERSON><PERSON>", "description": "Tools metadata for the Agent"}, "deployment": {"type": "string", "title": "Deployment", "description": "Deployment for the Agent"}, "deployment_config": {"anyOf": [{"additionalProperties": {"type": "string"}, "type": "object"}, {"type": "null"}], "title": "Deployment Config", "description": "Deployment config for the Agent"}, "model": {"type": "string", "title": "Model", "description": "Model for the Agent"}, "organization_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Organization ID for the Agent"}, "is_private": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Private", "description": "If the Agent is private", "default": false}}, "type": "object", "required": ["name", "deployment", "model"], "title": "CreateAgentRequest", "description": "<PERSON><PERSON><PERSON> to create an agent"}, "CreateAgentToolMetadataRequest": {"properties": {"id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "ID", "description": "Agent <PERSON><PERSON>"}, "tool_name": {"type": "string", "title": "Tool Name", "description": "Tool Name for the agent tool metadata"}, "artifacts": {"items": {"type": "object"}, "type": "array", "title": "Artifacts", "description": "Artifacts for the agent tool metadata"}}, "type": "object", "required": ["tool_name", "artifacts"], "title": "CreateAgentToolMetadataRequest", "description": "Request to create Agent <PERSON><PERSON>"}, "CreateGroup": {"properties": {"schemas": {"items": {"type": "string"}, "type": "array", "title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> for the group"}, "members": {"items": {"$ref": "#/components/schemas/GroupMember"}, "type": "array", "title": "Members", "description": "Members of the group"}, "displayName": {"type": "string", "title": "Display Name", "description": "Display name for the group"}}, "type": "object", "required": ["schemas", "members", "displayName"], "title": "CreateGroup"}, "CreateOrganization": {"properties": {"name": {"type": "string", "title": "Name", "description": "Name of the organization"}}, "type": "object", "required": ["name"], "title": "CreateOrganization", "description": "Request to create an organization"}, "CreateSnapshotRequest": {"properties": {"conversation_id": {"type": "string", "title": "Conversation ID", "description": "Unique identifier for the conversation"}}, "type": "object", "required": ["conversation_id"], "title": "CreateSnapshotRequest", "description": "Request to create a snapshot"}, "CreateSnapshotResponse": {"properties": {"snapshot_id": {"type": "string", "title": "Snapshot ID", "description": "Unique identifier for the snapshot"}, "link_id": {"type": "string", "title": "Link ID", "description": "Unique identifier for the link"}, "messages": {"items": {"$ref": "#/components/schemas/Message"}, "type": "array", "title": "Messages", "description": "List of messages"}}, "type": "object", "required": ["snapshot_id", "link_id", "messages"], "title": "CreateSnapshotResponse", "description": "Response for creating a snapshot"}, "DeleteAgent": {"properties": {}, "type": "object", "title": "DeleteAgent", "description": "Response for deleting an agent"}, "DeleteAgentFileResponse": {"properties": {}, "type": "object", "title": "DeleteAgentFileResponse", "description": "Response for deleting an agent file"}, "DeleteAgentToolMetadata": {"properties": {}, "type": "object", "title": "DeleteAgentToolMetadata", "description": "Delete agent tool metadata response"}, "DeleteConversationFileResponse": {"properties": {}, "type": "object", "title": "DeleteConversationFileResponse", "description": "Response for deleting a conversation file"}, "DeleteConversationResponse": {"properties": {}, "type": "object", "title": "DeleteConversationResponse", "description": "Response for deleting a conversation"}, "DeleteDeployment": {"properties": {}, "type": "object", "title": "DeleteDeployment", "description": "Delete Deployment Response"}, "DeleteModel": {"properties": {}, "type": "object", "title": "DeleteModel", "description": "Response for deleting a model"}, "DeleteOrganization": {"properties": {}, "type": "object", "title": "DeleteOrganization", "description": "Response when deleting organization"}, "DeleteSnapshotLinkResponse": {"properties": {}, "type": "object", "title": "DeleteSnapshotLinkResponse", "description": "Response for deleting a snapshot link"}, "DeleteSnapshotResponse": {"properties": {}, "type": "object", "title": "DeleteSnapshotResponse", "description": "Response for deleting a snapshot"}, "DeleteToolAuth": {"properties": {}, "type": "object", "title": "DeleteToolAuth", "description": "Response when deleting a tool auth"}, "DeleteUser": {"properties": {}, "type": "object", "title": "DeleteUser", "description": "Response when deleting a user"}, "DeploymentCreate": {"properties": {"id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "ID", "description": "Unique Identifier for the Deployment"}, "name": {"type": "string", "title": "Name", "description": "Name of the Deployment"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Description of the deployment"}, "deployment_class_name": {"type": "string", "title": "Deployment Class Name", "description": "Deployment Class Name"}, "is_community": {"type": "boolean", "title": "Is Community", "description": "Is the deployment from the commmunity", "default": false}, "default_deployment_config": {"additionalProperties": {"type": "string"}, "type": "object", "title": "Default Deployment Config", "description": "The default deployment configuration"}}, "type": "object", "required": ["name", "deployment_class_name", "default_deployment_config"], "title": "DeploymentCreate", "description": "Deployment Create Schema"}, "DeploymentDefinition": {"properties": {"id": {"type": "string", "title": "ID", "description": "Unique Identifier for the Deployment"}, "name": {"type": "string", "title": "Name", "description": "Name of the Deployment"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Description of the deployment"}, "config": {"additionalProperties": {"type": "string"}, "type": "object", "title": "Config", "description": "Config for the deployment", "default": {}}, "is_available": {"type": "boolean", "title": "Is Available", "description": "Is deployment is available", "default": false}, "is_community": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Community", "description": "Is the deployment from the commmunity", "default": false}, "models": {"items": {"type": "string"}, "type": "array", "title": "Models", "description": "List of models for the deployment"}, "class_name": {"type": "string", "title": "Class Name", "description": "Deployment class name"}}, "type": "object", "required": ["id", "name", "models", "class_name"], "title": "DeploymentDefinition", "description": "Deployment Definition"}, "DeploymentUpdate": {"properties": {"name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name", "description": "Name of the Deployment"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Description of the deployment"}, "deployment_class_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Deployment Class Name", "description": "Deployment Class Name"}, "is_community": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Community", "description": "Is the deployment from the commmunity"}, "default_deployment_config": {"anyOf": [{"additionalProperties": {"type": "string"}, "type": "object"}, {"type": "null"}], "title": "Default Deployment Config", "description": "The default deployment configuration"}}, "type": "object", "title": "DeploymentUpdate", "description": "Deployment Update Schema"}, "Document": {"properties": {"text": {"type": "string", "title": "Text", "description": "Document text"}, "document_id": {"type": "string", "title": "Document_Id", "description": "Unique Identifier for the document"}, "title": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Title", "description": "Document title"}, "url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "URL", "description": "Document URL"}, "fields": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Fields", "description": "Document Fields"}, "tool_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Tool Name", "description": "Tool name for the document"}}, "type": "object", "required": ["text", "document_id"], "title": "Document", "description": "Schema for a Document"}, "Email": {"properties": {"primary": {"type": "boolean", "title": "Primary", "description": "Is email the primary email"}, "value": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Value", "description": "Email value"}, "type": {"type": "string", "title": "Type", "description": "Type of email"}}, "type": "object", "required": ["primary", "type"], "title": "Email"}, "FileMetadata": {"properties": {"id": {"type": "string", "title": "ID", "description": "Unique identifier of the file"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At Timestamp", "description": "When file was created"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At Timestamp", "description": "When file was updated"}, "file_name": {"type": "string", "title": "File Name", "description": "Name of the file"}, "file_size": {"type": "integer", "minimum": 0.0, "title": "File Size", "description": "Size of the file in bytes", "default": 0}, "file_content": {"type": "string", "title": "File Content", "description": "The contents of the file"}}, "type": "object", "required": ["id", "created_at", "updated_at", "file_name", "file_content"], "title": "FileMetadata", "description": "Schema for file metadata"}, "GenerateTitleResponse": {"properties": {"title": {"type": "string", "title": "Title", "description": "Title generated for the conversation"}, "error": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Error", "description": "Error message if the response is an error"}}, "type": "object", "required": ["title"], "title": "GenerateTitleResponse", "description": "Response for generating a title"}, "Group": {"properties": {"schemas": {"items": {"type": "string"}, "type": "array", "title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> for the group"}, "members": {"items": {"$ref": "#/components/schemas/GroupMember"}, "type": "array", "title": "Members", "description": "Members of the group"}, "displayName": {"type": "string", "title": "Display Name", "description": "Display name for the group"}, "id": {"type": "string", "title": "ID", "description": "Unique identifier for the group"}, "meta": {"$ref": "#/components/schemas/Meta", "description": "<PERSON><PERSON><PERSON> for the group"}}, "type": "object", "required": ["schemas", "members", "displayName", "id", "meta"], "title": "Group"}, "GroupMember": {"properties": {"value": {"type": "string", "title": "Value", "description": "Value"}, "display": {"type": "string", "title": "Display", "description": "Display"}}, "type": "object", "required": ["value", "display"], "title": "GroupMember"}, "GroupOperation": {"properties": {"op": {"type": "string", "title": "Op", "description": "Op"}, "path": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Path", "description": "Path"}, "value": {"anyOf": [{"additionalProperties": {"type": "string"}, "type": "object"}, {"items": {"additionalProperties": {"type": "string"}, "type": "object"}, "type": "array"}], "title": "Value", "description": "Value"}}, "type": "object", "required": ["op", "value"], "title": "GroupOperation"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "JWTResponse": {"properties": {"token": {"type": "string", "title": "Token", "description": "JSON Web Token"}}, "type": "object", "required": ["token"], "title": "JWTResponse", "description": "JWT Response"}, "ListAuthStrategy": {"properties": {"strategy": {"type": "string", "title": "Strategy", "description": "Auth strategy name"}, "client_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Client ID", "description": "Client ID to be used"}, "authorization_endpoint": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization Endpoint", "description": "The endpoint for authorization"}, "pkce_enabled": {"type": "boolean", "title": "PKCE Enabled", "description": "If PKCE is enabled"}}, "type": "object", "required": ["strategy", "pkce_enabled"], "title": "ListAuthStrategy", "description": "List Auth Strategy"}, "ListConversationFile": {"properties": {"id": {"type": "string", "title": "ID", "description": "Unique identifier of the file"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At Timestamp", "description": "When file was created"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At Timestamp", "description": "When file was updated"}, "file_name": {"type": "string", "title": "File Name", "description": "Name of the file"}, "file_size": {"type": "integer", "minimum": 0.0, "title": "File Size", "description": "Size of the file in bytes", "default": 0}, "user_id": {"type": "string", "title": "User ID", "description": "Unique identifier for who created the file"}, "conversation_id": {"type": "string", "title": "Conversation ID", "description": "Unique identifier for the conversation the file is associated to"}}, "type": "object", "required": ["id", "created_at", "updated_at", "file_name", "user_id", "conversation_id"], "title": "ListConversationFile", "description": "Listing conversation files"}, "ListGroupResponse": {"properties": {"totalResults": {"type": "integer", "title": "Total Results", "description": "Total results available"}, "startIndex": {"type": "integer", "title": "Start Index", "description": "Start index for returned results"}, "itemsPerPage": {"type": "integer", "title": "Items Per Page", "description": "Total results returned in the request"}, "Resources": {"items": {"$ref": "#/components/schemas/Group"}, "type": "array", "title": "Resources", "description": "List of Groups"}}, "type": "object", "required": ["totalResults", "startIndex", "itemsPerPage", "Resources"], "title": "ListGroupResponse"}, "ListUserResponse": {"properties": {"totalResults": {"type": "integer", "title": "Total Results", "description": "Total results available"}, "startIndex": {"type": "integer", "title": "Start Index", "description": "Start index for returned results"}, "itemsPerPage": {"type": "integer", "title": "Items Per Page", "description": "Total results returned in the request"}, "Resources": {"items": {"$ref": "#/components/schemas/backend__schemas__scim__User"}, "type": "array", "title": "Resources", "description": "List of Users"}}, "type": "object", "required": ["totalResults", "startIndex", "itemsPerPage", "Resources"], "title": "ListUserResponse"}, "Login": {"properties": {"strategy": {"type": "string", "title": "Strategy", "description": "Auth strategy to use"}, "payload": {"anyOf": [{"additionalProperties": {"type": "string"}, "type": "object"}, {"type": "null"}], "title": "Payload", "description": "Login payload depending on strategy used"}}, "type": "object", "required": ["strategy"], "title": "<PERSON><PERSON>", "description": "Login Request"}, "Logout": {"properties": {}, "type": "object", "title": "Logout", "description": "Logout Request"}, "Message": {"properties": {"text": {"type": "string", "title": "Text", "description": "The text content of the message"}, "id": {"type": "string", "title": "ID", "description": "Unique identifier of the message"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At Timestamp", "description": "When message was created"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At Timestamp", "description": "When message was updated"}, "generation_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Generation ID", "description": "Generation ID for the message"}, "position": {"type": "integer", "title": "Position", "description": "Position in the conversation"}, "is_active": {"type": "boolean", "title": "Is Active", "description": "Is the message active"}, "documents": {"items": {"$ref": "#/components/schemas/Document"}, "type": "array", "title": "Documents", "description": "Documents associated with the message"}, "citations": {"items": {"$ref": "#/components/schemas/Citation"}, "type": "array", "title": "Citations", "description": "Citations associated with the message"}, "files": {"items": {"$ref": "#/components/schemas/ConversationFilePublic"}, "type": "array", "title": "Files", "description": "Files associated with the message"}, "tool_calls": {"items": {"$ref": "#/components/schemas/ToolCall"}, "type": "array", "title": "Tool Calls", "description": "Tool calls associated with the message"}, "tool_plan": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Tool Plan", "description": "Tool plan associated with the message"}, "agent": {"$ref": "#/components/schemas/MessageAgent", "title": "Agent", "description": "Agent associated with the message"}}, "type": "object", "required": ["text", "id", "created_at", "updated_at", "position", "is_active", "documents", "citations", "files", "tool_calls", "agent"], "title": "Message", "description": "Message Schema"}, "MessageAgent": {"type": "string", "enum": ["USER", "CHATBOT"], "title": "MessageAgent"}, "Meta": {"properties": {"resourceType": {"type": "string", "title": "Resource Type", "description": "Type of resource the metadata is for"}, "created": {"type": "string", "title": "Created", "description": "When metadata was created"}, "lastModified": {"type": "string", "title": "Last Modified", "description": "When metadata was last modified"}}, "type": "object", "required": ["resourceType", "created", "lastModified"], "title": "Meta", "description": "Schema for metadata"}, "Model": {"properties": {"name": {"type": "string", "title": "Name", "description": "Model name"}, "cohere_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Cohere Name", "description": "Cohere model name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Model description"}, "id": {"type": "string", "title": "ID", "description": "Unique identifier for the model"}, "deployment_id": {"type": "string", "title": "Deployment ID", "description": "Unique identifier for the deployment"}}, "type": "object", "required": ["name", "id", "deployment_id"], "title": "Model"}, "ModelCreate": {"properties": {"name": {"type": "string", "title": "Name", "description": "Model name"}, "cohere_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Cohere Name", "description": "Cohere model name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Model description"}, "deployment_id": {"type": "string", "title": "Deployment ID", "description": "Unique identifier for the deployment"}}, "type": "object", "required": ["name", "deployment_id"], "title": "ModelCreate"}, "ModelUpdate": {"properties": {"name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name", "description": "Model name"}, "cohere_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Cohere Name", "description": "Cohere model name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Model description"}, "deployment_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Deployment ID", "description": "Unique identifier for the deployment"}}, "type": "object", "title": "ModelUpdate"}, "Name": {"properties": {"givenName": {"type": "string", "title": "Given Name", "description": "User's given name"}, "familyName": {"type": "string", "title": "Family Name", "description": "User's family name"}}, "type": "object", "required": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>ame"], "title": "Name"}, "NonStreamedChatResponse": {"properties": {"response_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Response ID", "description": "Unique identifier for the response"}, "generation_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Generation ID", "description": "Unique identifier for the generation"}, "chat_history": {"anyOf": [{"items": {"$ref": "#/components/schemas/ChatMessage"}, "type": "array"}, {"type": "null"}], "title": "Chat History", "description": "A list of previous messages between the user and the model, meant to give the model conversational context for responding to the user's message."}, "finish_reason": {"type": "string", "title": "Finish Reason", "description": "Reason the chat stream ended"}, "text": {"type": "string", "title": "Text", "description": "Contents of the chat message"}, "citations": {"anyOf": [{"items": {"$ref": "#/components/schemas/Citation"}, "type": "array"}, {"type": "null"}], "title": "Citations", "description": "Citations for the chat message", "default": []}, "documents": {"anyOf": [{"items": {"$ref": "#/components/schemas/Document"}, "type": "array"}, {"type": "null"}], "title": "Documents", "description": "Documents used to generate grounded response with citations", "default": []}, "search_results": {"anyOf": [{"items": {"type": "object"}, "type": "array"}, {"type": "null"}], "title": "Search Results", "description": "Search results used to generate grounded response with citations", "default": []}, "search_queries": {"anyOf": [{"items": {"$ref": "#/components/schemas/SearchQuery"}, "type": "array"}, {"type": "null"}], "title": "Search Queries", "description": "List of generated search queries.", "default": []}, "conversation_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Conversation ID", "description": "To store a conversation then create a conversation id and use it for every related request"}, "tool_calls": {"anyOf": [{"items": {"$ref": "#/components/schemas/ToolCall"}, "type": "array"}, {"type": "null"}], "title": "Tool Calls", "description": "List of tool calls generated for custom tools", "default": []}, "error": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Error", "description": "Error message if the response is an error"}}, "type": "object", "required": ["finish_reason", "text"], "title": "NonStreamedChatResponse", "description": "Non streamed chat response"}, "Operation": {"properties": {"op": {"type": "string", "title": "Op", "description": "Op"}, "value": {"additionalProperties": {"type": "boolean"}, "type": "object", "title": "Value", "description": "Value"}}, "type": "object", "required": ["op", "value"], "title": "Operation"}, "Organization": {"properties": {"name": {"type": "string", "title": "Name", "description": "Name of the organization"}, "id": {"type": "string", "title": "ID", "description": "Unique identifier of the organization"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At Timestamp", "description": "When organization was created"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At Timestamp", "description": "When organization was updated"}}, "type": "object", "required": ["name", "id", "created_at", "updated_at"], "title": "Organization", "description": "Schema for an organization"}, "PatchGroup": {"properties": {"schemas": {"items": {"type": "string"}, "type": "array", "title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> for group"}, "operations": {"items": {"$ref": "#/components/schemas/GroupOperation"}, "type": "array", "title": "Operations", "description": "Operations for the group"}}, "type": "object", "required": ["schemas", "operations"], "title": "PatchGroup"}, "PatchUser": {"properties": {"schemas": {"items": {"type": "string"}, "type": "array", "title": "<PERSON><PERSON><PERSON>", "description": "Schemas for user"}, "operations": {"items": {"$ref": "#/components/schemas/Operation"}, "type": "array", "title": "Operations", "description": "Operations for the user"}}, "type": "object", "required": ["schemas", "operations"], "title": "PatchUser"}, "SearchQuery": {"properties": {"text": {"type": "string", "title": "Text", "description": "Text for the search"}, "generation_id": {"type": "string", "title": "Generation ID", "description": "Unique identifier for the generation"}}, "type": "object", "required": ["text", "generation_id"], "title": "SearchQuery", "description": "Schema for search query"}, "SnapshotData": {"properties": {"title": {"type": "string", "title": "Title", "description": "Title of the snapshot"}, "description": {"type": "string", "title": "Description", "description": "Description of the snapshot"}, "messages": {"items": {"$ref": "#/components/schemas/Message"}, "type": "array", "title": "Messages", "description": "List of messages"}}, "type": "object", "required": ["title", "description", "messages"], "title": "SnapshotData", "description": "Snapshot data"}, "SnapshotPublic": {"properties": {"conversation_id": {"type": "string", "title": "Conversation ID", "description": "Unique identifier for the conversation"}, "id": {"type": "string", "title": "ID", "description": "Unique identifier for the snapshot"}, "last_message_id": {"type": "string", "title": "Last Message ID", "description": "Unique identifier for the last message"}, "version": {"type": "integer", "title": "Version", "description": "Snapshot version"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At Timestamp", "description": "When snapshot was creted"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At Timestamp", "description": "When snapshot was updated"}, "snapshot": {"$ref": "#/components/schemas/SnapshotData", "title": "Snapshot Data", "description": "Data for the snapshot"}}, "type": "object", "required": ["conversation_id", "id", "last_message_id", "version", "created_at", "updated_at", "snapshot"], "title": "SnapshotPublic", "description": "Public snapshot"}, "SnapshotWithLinks": {"properties": {"conversation_id": {"type": "string", "title": "Conversation ID", "description": "Unique identifier for the conversation"}, "id": {"type": "string", "title": "ID", "description": "Unique identifier for the snapshot"}, "last_message_id": {"type": "string", "title": "Last Message ID", "description": "Unique identifier for the last message"}, "version": {"type": "integer", "title": "Version", "description": "Snapshot version"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At Timestamp", "description": "When snapshot was creted"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At Timestamp", "description": "When snapshot was updated"}, "snapshot": {"$ref": "#/components/schemas/SnapshotData", "title": "Snapshot Data", "description": "Data for the snapshot"}, "links": {"items": {"type": "string"}, "type": "array", "title": "Links", "description": "List of links"}}, "type": "object", "required": ["conversation_id", "id", "last_message_id", "version", "created_at", "updated_at", "snapshot", "links"], "title": "SnapshotWithLinks", "description": "Snapshot with links"}, "StreamCitationGeneration": {"properties": {"citations": {"items": {"$ref": "#/components/schemas/Citation"}, "type": "array", "title": "Citations", "description": "Citations for the chat message", "default": []}}, "type": "object", "title": "StreamCitationGeneration", "description": "Stream citation generation event"}, "StreamEnd": {"properties": {"message_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Message ID", "description": "Unique identifier for the message"}, "response_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Response ID", "description": "Unique identifier for the response"}, "generation_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Generation ID", "description": "Unique identifier for the generation"}, "conversation_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Conversation ID", "description": "Unique identifier for the conversation"}, "text": {"type": "string", "title": "Text", "description": "Contents of the chat message"}, "citations": {"items": {"$ref": "#/components/schemas/Citation"}, "type": "array", "title": "Citations", "description": "Citations for the chat messae.", "default": []}, "documents": {"items": {"$ref": "#/components/schemas/Document"}, "type": "array", "title": "Documents", "description": "Documents used to generate grounded response with citations", "default": []}, "search_results": {"items": {"type": "object"}, "type": "array", "title": "Search Results", "description": "Search results used to generate grounded response with citations", "default": []}, "search_queries": {"items": {"$ref": "#/components/schemas/SearchQuery"}, "type": "array", "title": "Search Queries", "description": "List of generated search queries", "default": []}, "tool_calls": {"items": {"$ref": "#/components/schemas/ToolCall"}, "type": "array", "title": "Tool Calls", "description": "List of tool calls generated for custom tools", "default": []}, "finish_reason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Finish Reason", "description": "<PERSON><PERSON> why the model finished the request"}, "chat_history": {"anyOf": [{"items": {"$ref": "#/components/schemas/ChatMessage"}, "type": "array"}, {"type": "null"}], "title": "Chat History", "description": "A list of entries used to construct the conversation. If provided, these messages will be used to build the prompt and the conversation_id will be ignored so no data will be stored to maintain state."}, "error": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Error", "description": "Error message if the response is an error"}}, "type": "object", "required": ["text"], "title": "StreamEnd", "description": "Stream end generation event"}, "StreamEvent": {"type": "string", "enum": ["stream-start", "search-queries-generation", "search-results", "tool-input", "tool-result", "text-generation", "citation-generation", "stream-end", "non-streamed-chat-response", "tool-calls-generation", "tool-calls-chunk"], "title": "StreamEvent", "description": "Stream Events returned by Cohere's chat stream response."}, "StreamQueryGeneration": {"properties": {"query": {"type": "string", "title": "Query", "description": "Search query used to generate grounded response with citations"}}, "type": "object", "required": ["query"], "title": "StreamQueryGeneration", "description": "Stream query generation event"}, "StreamSearchQueriesGeneration": {"properties": {"search_queries": {"items": {"$ref": "#/components/schemas/SearchQuery"}, "type": "array", "title": "Search Queries", "description": "Search query used to generate grounded response with citations", "default": []}}, "type": "object", "title": "StreamSearchQueriesGeneration", "description": "Stream queries generation event"}, "StreamSearchResults": {"properties": {"search_results": {"items": {"type": "object"}, "type": "array", "title": "Search Results", "description": "Search results used to generate grounded response with citations", "default": []}, "documents": {"items": {"$ref": "#/components/schemas/Document"}, "type": "array", "title": "Documents", "description": "Documents used to generate grounded response with citations", "default": []}}, "type": "object", "title": "StreamSearchResults", "description": "Stream search generation event"}, "StreamStart": {"properties": {"generation_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Generation ID", "description": "Generation ID for the event"}, "conversation_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Conversation ID", "description": "Conversation ID for the event"}}, "type": "object", "title": "StreamStart", "description": "Stream start event"}, "StreamTextGeneration": {"properties": {"text": {"type": "string", "title": "Text", "description": "Contents of the chat message"}}, "type": "object", "required": ["text"], "title": "StreamTextGeneration", "description": "Stream text generation event"}, "StreamToolCallsChunk": {"properties": {"tool_call_delta": {"anyOf": [{"$ref": "#/components/schemas/ToolCallDelta"}, {"type": "null"}], "title": "Tool Call Delta", "description": "Partial tool call", "default": {}}, "text": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Text", "description": "Contents of the chat message"}}, "type": "object", "title": "StreamToolCallsChunk", "description": "Stream tool call chunk generated event"}, "StreamToolCallsGeneration": {"properties": {"stream_search_results": {"anyOf": [{"$ref": "#/components/schemas/StreamSearchResults"}, {"type": "null"}], "title": "Stream Search Results", "description": "Search results used to generate grounded response with citations"}, "tool_calls": {"anyOf": [{"items": {"$ref": "#/components/schemas/ToolCall"}, "type": "array"}, {"type": "null"}], "title": "Tool Calls", "description": "List of tool calls generated for custom tools", "default": []}, "text": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Text", "description": "Contents of the chat message"}}, "type": "object", "title": "StreamToolCallsGeneration", "description": "Stream tool calls generation event"}, "StreamToolInput": {"properties": {"input_type": {"$ref": "#/components/schemas/ToolInputType", "title": "Input Type", "description": "Tool input type"}, "tool_name": {"type": "string", "title": "Tool Name", "description": "Name of the tool to be used"}, "input": {"type": "string", "title": "Input", "description": "Tool input"}, "text": {"type": "string", "title": "Text", "description": "Contents of the chat message"}}, "type": "object", "required": ["input_type", "tool_name", "input", "text"], "title": "StreamToolInput", "description": "Stream tool input generation event"}, "StreamToolResult": {"properties": {"result": {"title": "Result", "description": "Result from the tool"}, "tool_name": {"type": "string", "title": "Tool Name", "description": "Name of tool that generated the result"}, "documents": {"items": {"$ref": "#/components/schemas/Document"}, "type": "array", "title": "Documents", "description": "Documents used to generate grounded response with citations", "default": []}}, "type": "object", "required": ["result", "tool_name"], "title": "StreamToolResult", "description": "Stream tool result generation event"}, "ToggleConversationPinRequest": {"properties": {"is_pinned": {"type": "boolean", "title": "Is Pinned", "description": "If conversation is pinned"}}, "type": "object", "required": ["is_pinned"], "title": "ToggleConversationPinRequest", "description": "Request to toggle pinning a conversation"}, "Tool": {"properties": {"name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name", "description": "Name of the Tool", "default": ""}, "parameter_definitions": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Parameter Definitions", "description": "Parameters definitions for the tool", "default": {}}}, "type": "object", "title": "Tool", "description": "<PERSON><PERSON>"}, "ToolCall": {"properties": {"name": {"type": "string", "title": "Name", "description": "Name of the Tool"}, "parameters": {"type": "object", "title": "Parameters", "description": "Parameters for the tool call", "default": {}}}, "type": "object", "required": ["name"], "title": "ToolCall", "description": "<PERSON><PERSON><PERSON> for Tool Call"}, "ToolCallDelta": {"properties": {"name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name", "description": "Name of the Tool"}, "index": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Index", "description": "Index"}, "parameters": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Parameters", "description": "Parameters for the tool call"}}, "type": "object", "title": "ToolCallDelta", "description": "<PERSON><PERSON><PERSON> for Tool Call Delta"}, "ToolCategory": {"type": "string", "enum": ["Data loader", "File loader", "Function", "Web search"], "title": "ToolCategory", "description": "Supported Tool Categories"}, "ToolDefinition": {"properties": {"name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name", "description": "Name of the Tool", "default": ""}, "parameter_definitions": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Parameter Definitions", "description": "Parameters definitions for the tool", "default": {}}, "display_name": {"type": "string", "title": "Display Name", "description": "Display name for the tool", "default": ""}, "description": {"type": "string", "title": "Description", "description": "Description of the tool", "default": ""}, "error_message": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Error Message", "description": "Error message", "default": ""}, "kwargs": {"type": "object", "title": "kwargs", "description": "kwags for the tool", "default": {}}, "is_visible": {"type": "boolean", "title": "Is Visible", "description": "Is the tool visible", "default": false}, "is_available": {"type": "boolean", "title": "Is Available", "description": "Is the tool available", "default": false}, "category": {"$ref": "#/components/schemas/ToolCategory", "title": "Category", "description": "Tool category", "default": "Data loader"}, "is_auth_required": {"type": "boolean", "title": "<PERSON> Required", "description": "Is auth required for the tool", "default": false}, "auth_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Auth Url", "description": "Auth url for the tool", "default": ""}, "token": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Token", "description": "Token for the tool", "default": ""}, "should_return_token": {"type": "boolean", "title": "Should Return Token", "description": "If the tool returns a token", "default": false}}, "type": "object", "title": "ToolDefinition", "description": "Tool Definition Schema"}, "ToolInputType": {"type": "string", "enum": ["QUERY", "CODE"], "title": "ToolInputType", "description": "Type of input passed to the tool"}, "UpdateAgentRequest": {"properties": {"name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name", "description": "Name of the Agent"}, "version": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Version", "description": "Version of the Agent"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Agent Description"}, "preamble": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Preamble", "description": "The preamble for the Agent"}, "temperature": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Temperature", "description": "The temperature for the Agent"}, "tools": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Tools", "description": "List of tools for the Agent"}, "organization_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization ID", "description": "Organization ID for the Agent"}, "is_private": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Private", "description": "If the Agent is private"}, "deployment": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Deployment", "description": "Deployment for the Agent"}, "model": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Model", "description": "Model for the Agent"}, "tools_metadata": {"anyOf": [{"items": {"$ref": "#/components/schemas/CreateAgentToolMetadataRequest"}, "type": "array"}, {"type": "null"}], "title": "<PERSON><PERSON>", "description": "Tools metadata for the Agent"}}, "type": "object", "title": "UpdateAgentRequest", "description": "<PERSON><PERSON><PERSON> to update an agent"}, "UpdateAgentToolMetadataRequest": {"properties": {"id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "ID", "description": "Agent <PERSON><PERSON>"}, "tool_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Tool Name", "description": "Tool Name for the agent tool metadata"}, "artifacts": {"anyOf": [{"items": {"type": "object"}, "type": "array"}, {"type": "null"}], "title": "Artifacts", "description": "Artifacts for the agent tool metadata"}}, "type": "object", "title": "UpdateAgentToolMetadataRequest", "description": "Request to update Agent <PERSON><PERSON>"}, "UpdateConversationRequest": {"properties": {"title": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Title", "description": "Title of the conversation"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Description of the conversation"}}, "type": "object", "title": "UpdateConversationRequest", "description": "Request to update a conversation"}, "UpdateDeploymentEnv": {"properties": {"env_vars": {"additionalProperties": {"type": "string"}, "type": "object", "title": "Env <PERSON>", "description": "Environment Variables for the Deployment"}}, "type": "object", "required": ["env_vars"], "title": "UpdateDeploymentEnv", "description": "Request to update Deployment Environment Variables"}, "UpdateOrganization": {"properties": {"name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name", "description": "Name of the organization"}}, "type": "object", "title": "UpdateOrganization", "description": "Request to update an organization"}, "UploadAgentFileResponse": {"properties": {"id": {"type": "string", "title": "ID", "description": "Unique identifier of the file"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At Timestamp", "description": "When file was created"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At Timestamp", "description": "When file was updated"}, "file_name": {"type": "string", "title": "File Name", "description": "Name of the file"}, "file_size": {"type": "integer", "minimum": 0.0, "title": "File Size", "description": "Size of the file in bytes", "default": 0}}, "type": "object", "required": ["id", "created_at", "updated_at", "file_name"], "title": "UploadAgentFileResponse", "description": "Reponse for uploading an agent file"}, "UploadConversationFileResponse": {"properties": {"id": {"type": "string", "title": "ID", "description": "Unique identifier of the file"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At Timestamp", "description": "When file was created"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At Timestamp", "description": "When file was updated"}, "file_name": {"type": "string", "title": "File Name", "description": "Name of the file"}, "file_size": {"type": "integer", "minimum": 0.0, "title": "File Size", "description": "Size of the file in bytes", "default": 0}, "user_id": {"type": "string", "title": "User ID", "description": "Unique identifier for who created the file"}, "conversation_id": {"type": "string", "title": "Conversation ID", "description": "Unique identifier for the conversation the file is associated to"}}, "type": "object", "required": ["id", "created_at", "updated_at", "file_name", "user_id", "conversation_id"], "title": "UploadConversationFileResponse", "description": "Response for uploading a conversation file"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}, "backend__schemas__scim__CreateUser": {"properties": {"userName": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "User Name", "description": "User name"}, "active": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Active", "description": "Is user active"}, "schemas": {"items": {"type": "string"}, "type": "array", "title": "<PERSON><PERSON><PERSON>", "description": "Schemas for the user"}, "name": {"$ref": "#/components/schemas/Name", "title": "Name", "description": "Name of user"}, "emails": {"items": {"$ref": "#/components/schemas/Email"}, "type": "array", "title": "Emails", "description": "List of emails for user"}, "externalId": {"type": "string", "title": "External ID", "description": "External ID for the user"}}, "type": "object", "required": ["schemas", "name", "emails", "externalId"], "title": "CreateUser"}, "backend__schemas__scim__UpdateUser": {"properties": {"userName": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "User Name", "description": "User name"}, "active": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Active", "description": "Is user active"}, "schemas": {"items": {"type": "string"}, "type": "array", "title": "<PERSON><PERSON><PERSON>", "description": "Schemas for the user"}, "name": {"$ref": "#/components/schemas/Name", "description": "Name of user"}, "emails": {"items": {"$ref": "#/components/schemas/Email"}, "type": "array", "title": "Emails", "description": "List of emails for user"}}, "type": "object", "required": ["schemas", "name", "emails"], "title": "UpdateUser"}, "backend__schemas__scim__User": {"properties": {"userName": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "User Name", "description": "User name"}, "active": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Active", "description": "Is user active"}, "schemas": {"items": {"type": "string"}, "type": "array", "title": "<PERSON><PERSON><PERSON>", "description": "Schemas for the user"}, "id": {"type": "string", "title": "ID", "description": "Unique identifier for the user"}, "externalId": {"type": "string", "title": "External ID", "description": "External ID for the user"}, "meta": {"$ref": "#/components/schemas/Meta", "description": "Metadata for the user"}}, "type": "object", "required": ["schemas", "id", "externalId", "meta"], "title": "User"}, "backend__schemas__user__CreateUser": {"properties": {"password": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Password", "description": "Password for the user"}, "hashed_password": {"anyOf": [{"type": "string", "format": "binary"}, {"type": "null"}], "title": "Hashed Password", "description": "The user's password hashed"}, "fullname": {"type": "string", "title": "Full Name", "description": "User's Full Name"}, "email": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Email", "description": "User's email address"}}, "type": "object", "required": ["fullname"], "title": "CreateUser", "description": "Request to create a user"}, "backend__schemas__user__UpdateUser": {"properties": {"password": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Password", "description": "Password for the user"}, "hashed_password": {"anyOf": [{"type": "string", "format": "binary"}, {"type": "null"}], "title": "Hashed Password", "description": "The user's password hashed"}, "fullname": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Full Name", "description": "User's Full Name"}, "email": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Email", "description": "User's email address"}}, "type": "object", "title": "UpdateUser", "description": "Request to update a user"}, "backend__schemas__user__User": {"properties": {"fullname": {"type": "string", "title": "Full Name", "description": "User's Full Name"}, "email": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Email", "description": "User's email address"}, "id": {"type": "string", "title": "ID", "description": ""}, "created_at": {"type": "string", "format": "date-time", "title": "Created At Timestamp", "description": "When the user was created"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At Timestamp", "description": "When the user was updated"}}, "type": "object", "required": ["fullname", "id", "created_at", "updated_at"], "title": "User", "description": "User schema"}}, "securitySchemes": {"HTTPBasic": {"type": "http", "scheme": "basic"}}}}