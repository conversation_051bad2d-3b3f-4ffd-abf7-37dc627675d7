# The manifest for the "toolkit-app-api" service.
# Read the full specification for the "Backend Service" type at:
#  https://aws.github.io/copilot-cli/docs/manifest/backend-service/

# Your service name will be used in naming your resources like log groups, ECS services, etc.
name: toolkit-app-api
type: Backend Service

# Your service does not allow any traffic.

# Configuration for your containers and service.
image:
  # Docker build arguments. For additional overrides: https://aws.github.io/copilot-cli/docs/manifest/lb-web-service/#image-build
  build:
    context: .
    args:
      INSTALL_COMMUNITY_DEPS: false
    dockerfile: aws_copilot_deploy/aws-api.Dockerfile
  # Port exposed through your container to route traffic to it.
  port: 8000

cpu: 256       # Number of CPU units for the task.
memory: 1024    # Amount of memory in MiB used by the task.
platform: linux/x86_64     # See https://aws.github.io/copilot-cli/docs/manifest/backend-service/#platform
count: 1       # Number of tasks that should be running in your service.
exec: true     # Enable running commands in your container.

# storage:
  # readonly_fs: true       # Limit to read-only access to mounted root filesystems.

# Optional fields for more advanced use-cases.
#
variables:                    # Pass environment variables as key value pairs.
  PYTHON_INTERPRETER_URL: http://toolkit-app-terrarium.dev.toolkit-app.local:8080
#  LOG_LEVEL: info

#secrets:                      # Pass secrets from AWS Systems Manager (SSM) Parameter Store.
#  GITHUB_TOKEN: GITHUB_TOKEN  # The key is the name of the environment variable, the value is the name of the SSM parameter.

# You can override any of the values defined above by environment.
#environments:
#  test:
#    count: 2               # Number of tasks to run for the "test" environment.
#    deployment:            # The deployment strategy for the "test" environment.
#       rolling: 'recreate' # Stops existing tasks before new ones are started for faster deployments.