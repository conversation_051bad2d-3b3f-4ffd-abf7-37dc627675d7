Thank you for contributing to the Cohere Toolkit!

- [ ] **PR title**: "area: description"

  - Where "area" is whichever of interface, frontend, model, tools, backend, etc. is being modified. Use "docs: ..." for purely docs changes, "infra: ..." for CI changes.
  - Example: "deployment: add Azure model option"

- [ ] **PR message**: **_Delete this entire checklist_** and replace with

  - **Description:** a description of the change
  - **Issue:** the issue # it fixes, if applicable
  - **Dependencies:** any dependencies required for this change

- [ ] **Add tests and docs**: Please include testing and documentation for your changes
- [ ] **Lint and test**: Run `make lint` and `make run-unit-tests`

**AI Description**

<!-- begin-generated-description -->

<!--
  New: command-r-plus will maintain a pr description for you!
  Simply delete this section to opt out.
  Content inside this section will be overwritten by command as the PR is updated.
-->

<!-- end-generated-description -->
