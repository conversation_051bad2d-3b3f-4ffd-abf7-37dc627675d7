name: Backend - Unit tests

on:
  push:
    branches:
      - main
    paths:
      - src/backend/**
  pull_request:
    paths:
      - src/backend/**

jobs:
  pytest:
    permissions: write-all
    # environment: development
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repo
        uses: actions/checkout@v3

      - uses: actions/setup-python@v5
        with:
          python-version: '3.11'
          cache: 'pip'

      - name: Install poetry
        uses: snok/install-poetry@v1
        with:
          virtualenvs-create: true
          virtualenvs-in-project: true
          virtualenvs-path: .venv
          installer-parallel: true

      - name: Load cached venv
        id: cached-poetry-dependencies
        uses: actions/cache@v4
        with:
          path: .venv
          key: venv-${{ runner.os }}-${{ steps.setup-python.outputs.python-version }}-${{ hashFiles('**/poetry.lock') }}

      - name: Install dependencies
        if: steps.cached-poetry-dependencies.outputs.cache-hit != 'true'
        run: poetry install --with dev --no-interaction --no-root

      - name: Setup test DB container
        run: make test-db

      - name: Test with pytest
        if: github.actor != 'dependabot[bot]'
        run: |
          make run-unit-tests
        env:
          PYTHONPATH: src

      - name: Upload coverage reports to Codecov
        uses: codecov/codecov-action@v4.0.1
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          file: coverage.xml
