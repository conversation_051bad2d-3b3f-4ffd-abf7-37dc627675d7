name: Backend - Lint

on:
  push:
    branches:
      - main
    paths:
      - src/backend/**
  pull_request:
    paths:
      - src/backend/**

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          clean: true
      - name: Run lint checks
        uses: astral-sh/ruff-action@v3
        with:
          src: './src/'
          version: 0.6.0
