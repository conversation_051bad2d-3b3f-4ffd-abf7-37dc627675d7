name: Coral Web - Format, Build & Validate

on:
  push:
    branches: [main]
    paths:
      - src/interfaces/coral_web/**
  pull_request: {}

jobs:
  interface_tests:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: src/interfaces/coral_web
    steps:
      - name: Checkout repo
        uses: actions/checkout@v3
        with:
          fetch-depth: 2
      - name: Start backend (for OpenAPI)
        run: docker compose up backend -d
      - name: Set up Node.js and install dependencies
        uses: ./.github/actions/coral-web-install
      - name: Check format
        run: npm run format
      - name: Check build
        run: npm run build
      - name: Generate client
        run: npm run generate:client && npm run format:write
      - name: Check generated client
        run: git diff --exit-code ./src/cohere-client/generated || (echo "Generated client is outdated. Please regenerate and commit." && exit 1)
