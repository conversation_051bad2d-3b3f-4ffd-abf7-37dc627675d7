name: Frontend - Create and publish Docker image

on:
  release:
    types: [published]
  workflow_dispatch:
    inputs:
      tags:
        description: 'Docker image tag (e.g: v0.0.1)'
        required: false
        type: string

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: cohere-toolkit-frontend

jobs:
  build-and-push-frontend-image:
    runs-on: ubuntu-latest
    # Sets the permissions granted to the `GITHUB_TOKEN` for the actions in this job.
    permissions:
      contents: read
      packages: write
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Log in to the Container registry
        uses: docker/login-action@65b78e6e13532edd9afa3aa52ac7964289d1a9c1
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@9ec57ed1fcdbf14dcef7dfbe97b2010124a938b7
        with:
          images: ${{ env.REGISTRY }}/cohere-ai/${{ env.IMAGE_NAME }}
      - name: Build and push Docker image
        uses: docker/build-push-action@f2a1d5e99d037542a71f64918e516c093c6f3fc4
        with:
          context: ./src/interfaces/coral_web
          tags: ${{ steps.meta.outputs.tags }}
          push: true
