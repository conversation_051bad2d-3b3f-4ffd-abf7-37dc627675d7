name: Backend - Typecheck new files

on:
  push:
    branches:
      - main
    paths:
      - src/backend/**
  pull_request:
    paths:
      - src/backend/**

jobs:
  check:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - run: pipx install poetry
      - uses: actions/setup-python@v4
        with:
          python-version: '3.11'
          cache: 'poetry'
      - run: poetry install
      - run: echo "$(poetry env info --path)/bin" >> $GITHUB_PATH
      - name: Get new files added in the PR
        id: get_new_files
        run: |
          git fetch origin main
          NEW_PY_FILES=$(git diff --name-only --diff-filter=A origin/main HEAD | grep '\.py$' | tr '\n' ' ')
          echo "New files: $NEW_PY_FILES"
          echo "new_py_files=$NEW_PY_FILES" >> $GITHUB_OUTPUT
      - name: Typecheck new files
        uses: jakebailey/pyright-action@v2
        with:
          version: 1.1.311
          extra-args: ${{ steps.get_new_files.outputs.new_py_files }}
