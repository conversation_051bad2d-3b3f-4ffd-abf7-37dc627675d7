name: Alembic - No duplicate IDs

on:
  pull_request:
    paths:
      - 'src/backend/alembic/versions/**'
  push:
    paths:
      - 'src/backend/alembic/versions/**'

jobs:
  check-alembic-revision-ids:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v2

    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: 3.8

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install alembic

    - name: Check for duplicate revision IDs
      run: python .github/scripts/check_revision_ids.py
