name: <PERSON><PERSON><PERSON>t - Format, Build & Validate

on:
  push:
    branches: [main]
    paths:
      - src/interfaces/slack_bot/**
  pull_request: {}

jobs:
  interface_tests:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: src/interfaces/slack_bot
    steps:
      - name: Checkout repo
        uses: actions/checkout@v3
        with:
          fetch-depth: 2
      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version-file: "src/interfaces/slack_bot/.nvmrc"
          cache: "npm"
          cache-dependency-path: "src/interfaces/slack_bot/package-lock.json"
      - name: Install dependencies with npm
        shell: bash
        working-directory: src/interfaces/slack_bot
        run: npm ci
      - name: Check format
        run: npm run format
      - name: Check types
        run: npm run tsc:check
      - name: Run tests
        run: npm run test
      - name: Check build
        run: npm run build
