{{- $values := .Values.frontend}}

apiVersion: v1
kind: ServiceAccount
metadata:
  name: toolkit-frontend
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "cohere-toolkit.labels" . | nindent 4 }}
  {{- with $values.serviceAccount.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
{{- with $values.serviceAccount.imagePullSecrets }}
imagePullSecrets:
  {{- toYaml . | nindent 2 }}
{{- end }}

---

apiVersion: apps/v1
kind: Deployment
metadata:
  name: toolkit-frontend
  namespace: {{ .Release.Namespace }}
  labels:
    app.kubernetes.io/name: toolkit-frontend
    {{- include "cohere-toolkit.labels" . | nindent 4 }}
spec:
  replicas: {{ $values.replicaCounts }}
  selector:
    matchLabels:
      app.kubernetes.io/name: toolkit-frontend
  template:
    metadata:
      name: toolkit-frontend
      labels:
        app.kubernetes.io/name: toolkit-frontend
        {{- include "cohere-toolkit.labels" . | nindent 8 }}
      {{- with $values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    spec:
      serviceAccountName: toolkit-frontend
      {{- with $values.podSecurityContext }}
      securityContext:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with $values.initContainers }}
      initContainers:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      containers:
        - name: toolkit-frontend
          image: {{ $values.image.repository }}:{{ $values.image.tag | default .Chart.AppVersion }}
          imagePullPolicy: {{ $values.image.pullPolicy }}
          {{- with $values.securityContext }}
          securityContext:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          {{- with $values.resources }}
          resources:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          ports:
            - containerPort: 4000
              protocol: TCP
              name: http
          envFrom:
            - secretRef:
                name: toolkit-frontend
                optional: false
          {{- with $values.livenessProbe }}
          livenessProbe:
              {{- toYaml . | nindent 12 }}
          {{- end }}
          {{- with $values.readinessProbe }}
          readinessProbe:
              {{- toYaml . | nindent 12 }}
          {{- end }}
          {{- with $values.sidecars }}
          {{- toYaml . | nindent 12 }}
          {{- end }}
      restartPolicy: Always
      {{- with $values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with $values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with $values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}

---

apiVersion: v1
kind: Service
metadata:
  name: toolkit-frontend
  namespace: {{ .Release.Namespace }}
  labels:
    app.kubernetes.io/name: toolkit-frontend
    {{- include "cohere-toolkit.labels" . | nindent 4 }}
spec:
  selector:
    app.kubernetes.io/name: toolkit-frontend
  ports:
    - protocol: TCP
      port: {{ $values.service.port }}
      targetPort: http
  type: {{ $values.service.type }}

---

{{- if $values.autoscaling.enabled }}
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: toolkit-frontend
  namespace: {{ .Release.Namespace }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: toolkit-frontend
  minReplicas: {{ $values.autoscaling.minReplicas }}
  maxReplicas: {{ $values.autoscaling.maxReplicas }}
  metrics:
    {{- if $values.autoscaling.targetCPUUtilizationPercentage }}
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: {{ $values.autoscaling.targetCPUUtilizationPercentage }}
    {{- end}}
    {{- if $values.autoscaling.targetMemoryUtilizationPercentage }}
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: {{ $values.autoscaling.targetMemoryUtilizationPercentage }}
    {{- end}}
{{- end}}

---

apiVersion: v1
kind: Secret
metadata:
  name: toolkit-frontend
  namespace: {{ .Release.Namespace }}
  labels:
    app.kubernetes.io/name: toolkit-frontend
    {{- include "cohere-toolkit.labels" . | nindent 4 }}
stringData:
  NEXT_PUBLIC_API_HOSTNAME: "{{ required "global.backend_url value is required" .Values.global.backend_url }}"
  {{- with $values.config }}
  {{- toYaml . | nindent 2 }}
  {{- end }}
