apiVersion: batch/v1
kind: Job
metadata:
  name: migrations
  namespace: {{ .Release.Namespace }}
  labels:
      {{- include "cohere-toolkit.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": pre-install
    "helm.sh/hook-weight": "-1"
    "helm.sh/hook-delete-policy": hook-succeeded,hook-failed
spec:
  template:
    spec:
      containers:
        - name: migrations
          image: {{ .Values.backend.image.repository }}:{{ .Values.backend.image.tag | default .Chart.AppVersion }}
          command:
            - poetry
            - run
          args:
            - alembic
            - -c
            - src/backend/alembic.ini
            - upgrade
            - head
          imagePullPolicy: IfNotPresent
          envFrom:
            - secretRef:
                name: toolkit-backend
                optional: false
      restartPolicy: OnFailure
