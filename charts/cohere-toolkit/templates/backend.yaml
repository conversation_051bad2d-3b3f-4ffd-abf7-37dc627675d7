{{- $values := .Values.backend}}

apiVersion: v1
kind: ServiceAccount
metadata:
  name: toolkit-backend
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "cohere-toolkit.labels" . | nindent 4 }}
  {{- with $values.serviceAccount.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
{{- with $values.serviceAccount.imagePullSecrets }}
imagePullSecrets:
  {{- toYaml . | nindent 2 }}
{{- end }}

---

apiVersion: apps/v1
kind: Deployment
metadata:
  name: toolkit-backend
  namespace: {{ .Release.Namespace }}
  labels:
    app.kubernetes.io/name: toolkit-backend
    {{- include "cohere-toolkit.labels" . | nindent 4 }}
spec:
  replicas: {{ $values.replicaCounts }}
  selector:
    matchLabels:
      app.kubernetes.io/name: toolkit-backend
  template:
    metadata:
      name: toolkit-backend
      labels:
        app.kubernetes.io/name: toolkit-backend
        {{ printf "%s-client" .Values.global.redis.host }}: "true"
        {{- include "cohere-toolkit.labels" . | nindent 8 }}
      {{- with $values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    spec:
      serviceAccountName: toolkit-backend
      {{- with $values.podSecurityContext }}
      securityContext:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with $values.initContainers }}
      initContainers:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      containers:
        - name: toolkit-backend
          image: {{ $values.image.repository }}:{{ $values.image.tag | default .Chart.AppVersion }}
          imagePullPolicy: {{ $values.image.pullPolicy }}
          {{- with $values.securityContext }}
          securityContext:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          {{- with $values.resources }}
          resources:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          ports:
            - containerPort: 8000
              protocol: TCP
              name: http
          envFrom:
            - secretRef:
                name: toolkit-backend
                optional: false
          {{- with $values.livenessProbe }}
          livenessProbe:
              {{- toYaml . | nindent 12 }}
          {{- end }}
          {{- with $values.readinessProbe }}
          readinessProbe:
              {{- toYaml . | nindent 12 }}
          {{- end }}
          {{- with $values.sidecars }}
          {{- toYaml . | nindent 12 }}
          {{- end }}
      restartPolicy: Always
      {{- with $values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with $values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with $values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}

---

apiVersion: v1
kind: Service
metadata:
  name: toolkit-backend
  namespace: {{ .Release.Namespace }}
  labels:
    app.kubernetes.io/name: toolkit-backend
    {{- include "cohere-toolkit.labels" . | nindent 4 }}
spec:
  selector:
    app.kubernetes.io/name: toolkit-backend
  ports:
    - protocol: TCP
      port: {{ $values.service.port }}
      targetPort: http
  type: {{ $values.service.type }}

---

{{- if $values.autoscaling.enabled }}
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: toolkit-backend
  namespace: {{ .Release.Namespace }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: toolkit-backend
  minReplicas: {{ $values.autoscaling.minReplicas }}
  maxReplicas: {{ $values.autoscaling.maxReplicas }}
  metrics:
    {{- if $values.autoscaling.targetCPUUtilizationPercentage }}
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: {{ $values.autoscaling.targetCPUUtilizationPercentage }}
    {{- end}}
    {{- if $values.autoscaling.targetMemoryUtilizationPercentage }}
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: {{ $values.autoscaling.targetMemoryUtilizationPercentage }}
    {{- end}}
{{- end}}

---

apiVersion: v1
kind: Secret
metadata:
  name: toolkit-backend
  namespace: {{ .Release.Namespace }}
  labels:
    app.kubernetes.io/name: toolkit-backend
    {{- include "cohere-toolkit.labels" . | nindent 4 }}
stringData:
  DATABASE_URL: "{{ template "cohere-toolkit.database-url" $ }}"
  REDIS_URL: "{{ template "cohere-toolkit.redis-url" $ }}"
  BROKER_URL: "{{ template "cohere-toolkit.redis-url" $ }}"
  {{- if .Values.global.cohere.api_key }}
  COHERE_API_KEY: "{{ .Values.global.cohere.api_key }}"
  {{- end }}
  {{- if .Values.terrarium.enabled }}
  PYTHON_INTERPRETER_URL: "http://terrarium"
  {{- end }}
  {{- with $values.config }}
  {{- toYaml . | nindent 2 }}
  {{- end }}
