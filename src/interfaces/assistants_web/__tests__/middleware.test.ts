import { NextRequest } from 'next/server';
import { describe, it, expect, vi, beforeEach } from 'vitest';

import { middleware } from '../middleware';

// Create mock functions that can be accessed in tests
const mockRedirect = vi.fn();
const mockNext = vi.fn();

// Mock NextResponse
vi.mock('next/server', async () => {
  const actual = await vi.importActual('next/server');

  return {
    ...actual,
    NextResponse: {
      redirect: mockRedirect,
      next: mockNext,
    },
  };
});

describe('middleware', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockNext.mockReturnValue('next-response');
    mockRedirect.mockReturnValue('redirect-response');
  });

  const createRequest = (pathname: string, organizationId?: string) => {
    const url = `https://example.com${pathname}`;
    const request = new NextRequest(url);
    
    if (organizationId) {
      request.cookies.set('organizationId', organizationId);
    }
    
    return request;
  };

  describe('static files and API routes', () => {
    it('should skip middleware for static files', () => {
      const request = createRequest('/_next/static/file.js');
      const result = middleware(request);
      
      expect(mockNext).toHaveBeenCalled();
      expect(mockRedirect).not.toHaveBeenCalled();
      expect(result).toBe('next-response');
    });

    it('should skip middleware for API routes', () => {
      const request = createRequest('/api/endpoint');
      const result = middleware(request);
      
      expect(mockNext).toHaveBeenCalled();
      expect(mockRedirect).not.toHaveBeenCalled();
    });

    it('should skip middleware for health check', () => {
      const request = createRequest('/healthz');
      const result = middleware(request);
      
      expect(mockNext).toHaveBeenCalled();
      expect(mockRedirect).not.toHaveBeenCalled();
    });

    it('should skip middleware for share routes', () => {
      const request = createRequest('/share/123');
      const result = middleware(request);
      
      expect(mockNext).toHaveBeenCalled();
      expect(mockRedirect).not.toHaveBeenCalled();
    });

    it('should skip middleware for auth routes', () => {
      const authRoutes = ['/auth', '/login', '/logout', '/register'];
      
      authRoutes.forEach(route => {
        const request = createRequest(route);
        middleware(request);
        expect(mockNext).toHaveBeenCalled();
      });
    });

    it('should skip middleware for files with extensions', () => {
      const request = createRequest('/favicon.ico');
      const result = middleware(request);
      
      expect(mockNext).toHaveBeenCalled();
      expect(mockRedirect).not.toHaveBeenCalled();
    });
  });

  describe('workspace paths', () => {
    it('should continue for existing workspace paths', () => {
      const workspacePaths = [
        '/workspace-123/c/conv-456',
        '/org-abc/a/agent-def/c/conv-789',
        '/workspace-123/discover',
        '/workspace-123/new',
        '/workspace-123/edit/agent-123',
        '/workspace-123/settings',
      ];

      workspacePaths.forEach(path => {
        const request = createRequest(path);
        const result = middleware(request);
        
        expect(mockNext).toHaveBeenCalled();
        expect(mockRedirect).not.toHaveBeenCalled();
        expect(result).toBe('next-response');
      });
    });
  });

  describe('legacy path redirects', () => {
    it('should redirect legacy conversation path to workspace path', () => {
      const request = createRequest('/c/conv-123', 'org-456');
      const result = middleware(request);
      
      expect(mockRedirect).toHaveBeenCalledWith(expect.objectContaining({
        href: 'https://example.com/org-456/c/conv-123',
      }));
      expect(result).toBe('redirect-response');
    });

    it('should redirect legacy agent path to workspace path', () => {
      const request = createRequest('/a/agent-123/c/conv-456', 'org-789');
      const result = middleware(request);
      
      expect(mockRedirect).toHaveBeenCalledWith(expect.objectContaining({
        href: 'https://example.com/org-789/a/agent-123/c/conv-456',
      }));
    });

    it('should redirect legacy discover path to workspace path', () => {
      const request = createRequest('/discover', 'org-123');
      const result = middleware(request);
      
      expect(mockRedirect).toHaveBeenCalledWith(expect.objectContaining({
        href: 'https://example.com/org-123/discover',
      }));
    });

    it('should redirect legacy new path to workspace path', () => {
      const request = createRequest('/new', 'org-123');
      const result = middleware(request);
      
      expect(mockRedirect).toHaveBeenCalledWith(expect.objectContaining({
        href: 'https://example.com/org-123/new',
      }));
    });

    it('should redirect legacy edit path to workspace path', () => {
      const request = createRequest('/edit/agent-123', 'org-456');
      const result = middleware(request);
      
      expect(mockRedirect).toHaveBeenCalledWith(expect.objectContaining({
        href: 'https://example.com/org-456/edit/agent-123',
      }));
    });

    it('should redirect legacy settings path to workspace path', () => {
      const request = createRequest('/settings', 'org-789');
      const result = middleware(request);
      
      expect(mockRedirect).toHaveBeenCalledWith(expect.objectContaining({
        href: 'https://example.com/org-789/settings',
      }));
    });

    it('should redirect to root when legacy path has no organization cookie', () => {
      const request = createRequest('/c/conv-123'); // No organization cookie
      const result = middleware(request);
      
      expect(mockRedirect).toHaveBeenCalledWith(expect.objectContaining({
        href: 'https://example.com/',
      }));
    });
  });

  describe('root path handling', () => {
    it('should redirect root to workspace when organization cookie exists', () => {
      const request = createRequest('/', 'org-123');
      const result = middleware(request);
      
      expect(mockRedirect).toHaveBeenCalledWith(expect.objectContaining({
        href: 'https://example.com/org-123',
      }));
      expect(result).toBe('redirect-response');
    });

    it('should continue for root when no organization cookie', () => {
      const request = createRequest('/'); // No organization cookie
      const result = middleware(request);
      
      expect(mockNext).toHaveBeenCalled();
      expect(mockRedirect).not.toHaveBeenCalled();
      expect(result).toBe('next-response');
    });
  });

  describe('other paths', () => {
    it('should continue for unmatched paths', () => {
      const request = createRequest('/some-other-path');
      const result = middleware(request);
      
      expect(mockNext).toHaveBeenCalled();
      expect(mockRedirect).not.toHaveBeenCalled();
      expect(result).toBe('next-response');
    });
  });
});
