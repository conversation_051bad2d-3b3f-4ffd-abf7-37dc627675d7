import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { useRouter } from 'next/navigation';
import { describe, it, expect, vi, beforeEach } from 'vitest';

import { CohereClientProvider, Organization } from '@/cohere-client';
import { 
  WorkspaceInvitation, 
  InvitationStatus, 
  WorkspaceRole, 
  WorkspaceMember,
  BillingInfo,
  BillingStatus,
  BillingPlan,
  BillingCycle
} from '@/types/invitation';
import { WorkspaceSettingsPage } from '@/app/(main)/[workspaceId]/settings/WorkspaceSettingsPage';

// Mock Next.js router
vi.mock('next/navigation', () => ({
  useRouter: vi.fn(),
}));

// Mock the CohereClient
const mockCohereClient = {
  listOrganizations: vi.fn(),
  setOrganizationId: vi.fn(),
  getOrganizationId: vi.fn(),
  createWorkspaceInvitation: vi.fn(),
  getWorkspaceMembers: vi.fn(),
  removeWorkspaceMember: vi.fn(),
  updateWorkspaceMemberRole: vi.fn(),
  getBillingInfo: vi.fn(),
  updateSeatCount: vi.fn(),
};

// Mock data
const mockOrganization: Organization = {
  id: 'workspace-123',
  name: 'Test Enterprise Workspace',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
};

const mockMembers: WorkspaceMember[] = [
  {
    id: 'member-1',
    user_id: 'user-1',
    workspace_id: 'workspace-123',
    email: '<EMAIL>',
    name: 'Admin User',
    role: WorkspaceRole.ADMIN,
    joined_at: '2024-01-01T00:00:00Z',
  },
  {
    id: 'member-2',
    user_id: 'user-2',
    workspace_id: 'workspace-123',
    email: '<EMAIL>',
    name: 'Member User',
    role: WorkspaceRole.MEMBER,
    joined_at: '2024-01-02T00:00:00Z',
  },
];

const mockBillingInfo: BillingInfo = {
  workspace_id: 'workspace-123',
  stripe_customer_id: 'cus_123',
  stripe_subscription_id: 'sub_123',
  plan_type: BillingPlan.PROFESSIONAL,
  seat_count: 10,
  billing_cycle: BillingCycle.MONTHLY,
  amount_per_seat: 25,
  currency: 'usd',
  next_billing_date: '2024-02-01T00:00:00Z',
  status: BillingStatus.ACTIVE,
};

// Mock the useCohereClient hook
vi.mock('@/cohere-client', async () => {
  const actual = await vi.importActual('@/cohere-client');
  return {
    ...actual,
    useCohereClient: () => mockCohereClient,
  };
});

// Mock the organization hook
vi.mock('@/hooks/use-organization', () => ({
  useCurrentOrganization: () => ({
    currentOrganization: mockOrganization,
  }),
}));

// Mock Settings component
vi.mock('@/app/(main)/settings/Settings', () => ({
  default: () => <div data-testid="general-settings">General Settings Content</div>,
}));

// Test wrapper
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      <CohereClientProvider client={mockCohereClient as any}>
        {children}
      </CohereClientProvider>
    </QueryClientProvider>
  );
};

describe('Enterprise Features E2E', () => {
  const mockPush = vi.fn();
  const mockRouter = { push: mockPush };

  beforeEach(() => {
    vi.clearAllMocks();
    (useRouter as any).mockReturnValue(mockRouter);
    
    // Setup default mock responses
    mockCohereClient.getWorkspaceMembers.mockResolvedValue(mockMembers);
    mockCohereClient.getBillingInfo.mockResolvedValue(mockBillingInfo);
  });

  it('should integrate workspace settings with member management and billing', async () => {
    const Wrapper = createWrapper();
    
    render(
      <Wrapper>
        <WorkspaceSettingsPage workspaceId="workspace-123" />
      </Wrapper>
    );

    // Should show workspace settings page
    expect(screen.getByText('Workspace Settings')).toBeInTheDocument();
    expect(screen.getByText('Manage your workspace configuration, members, and billing')).toBeInTheDocument();

    // Should show tab navigation
    expect(screen.getByText('General')).toBeInTheDocument();
    expect(screen.getByText('Members')).toBeInTheDocument();
    expect(screen.getByText('Billing')).toBeInTheDocument();

    // Should start on general tab
    expect(screen.getByTestId('general-settings')).toBeInTheDocument();
  });

  it('should handle member management workflow', async () => {
    const Wrapper = createWrapper();
    
    render(
      <Wrapper>
        <WorkspaceSettingsPage workspaceId="workspace-123" />
      </Wrapper>
    );

    // Switch to members tab
    const membersTab = screen.getByText('Members');
    fireEvent.click(membersTab);

    await waitFor(() => {
      expect(screen.getByText('Workspace Members')).toBeInTheDocument();
    });

    // Should show member list
    await waitFor(() => {
      expect(screen.getByText('Admin User')).toBeInTheDocument();
      expect(screen.getByText('Member User')).toBeInTheDocument();
    });

    // Should show invite button
    expect(screen.getByText('Invite User')).toBeInTheDocument();

    // Test inviting a user
    const inviteButton = screen.getByText('Invite User');
    fireEvent.click(inviteButton);

    await waitFor(() => {
      expect(screen.getByText('Invite User to Workspace')).toBeInTheDocument();
    });
  });

  it('should handle billing management workflow', async () => {
    const Wrapper = createWrapper();
    
    render(
      <Wrapper>
        <WorkspaceSettingsPage workspaceId="workspace-123" />
      </Wrapper>
    );

    // Switch to billing tab
    const billingTab = screen.getByText('Billing');
    fireEvent.click(billingTab);

    await waitFor(() => {
      expect(screen.getByText('Billing Overview')).toBeInTheDocument();
    });

    // Should show billing information
    await waitFor(() => {
      expect(screen.getByText('Professional Plan')).toBeInTheDocument();
      expect(screen.getByText('Active')).toBeInTheDocument();
      expect(screen.getByText('10')).toBeInTheDocument(); // seat count
      expect(screen.getByText('2 active members')).toBeInTheDocument();
    });

    // Should show manage seats button
    expect(screen.getByText('Manage Seats')).toBeInTheDocument();

    // Test seat management
    const manageSeatsButton = screen.getByText('Manage Seats');
    fireEvent.click(manageSeatsButton);

    await waitFor(() => {
      expect(screen.getByText('Manage Seats')).toBeInTheDocument();
    });
  });

  it('should handle complete invitation workflow', async () => {
    mockCohereClient.createWorkspaceInvitation.mockResolvedValue({
      id: 'invitation-123',
      status: InvitationStatus.PENDING,
    });

    const Wrapper = createWrapper();
    
    render(
      <Wrapper>
        <WorkspaceSettingsPage workspaceId="workspace-123" />
      </Wrapper>
    );

    // Navigate to members tab
    const membersTab = screen.getByText('Members');
    fireEvent.click(membersTab);

    await waitFor(() => {
      expect(screen.getByText('Invite User')).toBeInTheDocument();
    });

    // Open invite modal
    const inviteButton = screen.getByText('Invite User');
    fireEvent.click(inviteButton);

    await waitFor(() => {
      expect(screen.getByLabelText('Email Address')).toBeInTheDocument();
    });

    // Fill out invitation form
    const emailInput = screen.getByLabelText('Email Address');
    const roleSelect = screen.getByLabelText('Role');
    const messageInput = screen.getByLabelText('Personal Message (Optional)');

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(roleSelect, { target: { value: WorkspaceRole.MEMBER } });
    fireEvent.change(messageInput, { target: { value: 'Welcome to our team!' } });

    // Submit invitation
    const sendButton = screen.getByText('Send Invitation');
    fireEvent.click(sendButton);

    await waitFor(() => {
      expect(mockCohereClient.createWorkspaceInvitation).toHaveBeenCalledWith({
        workspace_id: 'workspace-123',
        invitee_email: '<EMAIL>',
        role: WorkspaceRole.MEMBER,
        message: 'Welcome to our team!',
      });
    });
  });

  it('should handle complete seat management workflow', async () => {
    mockCohereClient.updateSeatCount.mockResolvedValue({
      ...mockBillingInfo,
      seat_count: 15,
    });

    const Wrapper = createWrapper();
    
    render(
      <Wrapper>
        <WorkspaceSettingsPage workspaceId="workspace-123" />
      </Wrapper>
    );

    // Navigate to billing tab
    const billingTab = screen.getByText('Billing');
    fireEvent.click(billingTab);

    await waitFor(() => {
      expect(screen.getByText('Manage Seats')).toBeInTheDocument();
    });

    // Open seat management modal
    const manageSeatsButton = screen.getByText('Manage Seats');
    fireEvent.click(manageSeatsButton);

    await waitFor(() => {
      expect(screen.getByDisplayValue('10')).toBeInTheDocument();
    });

    // Increase seat count
    const seatInput = screen.getByDisplayValue('10');
    fireEvent.change(seatInput, { target: { value: '15' } });

    await waitFor(() => {
      expect(screen.getByText('Additional Cost')).toBeInTheDocument();
      expect(screen.getByText('+5 seats')).toBeInTheDocument();
    });

    // Submit seat update
    const addSeatsButton = screen.getByText('Add Seats');
    fireEvent.click(addSeatsButton);

    await waitFor(() => {
      expect(mockCohereClient.updateSeatCount).toHaveBeenCalledWith({
        workspaceId: 'workspace-123',
        seatCount: 15,
      });
    });
  });

  it('should handle member role updates', async () => {
    mockCohereClient.updateWorkspaceMemberRole.mockResolvedValue({
      ...mockMembers[1],
      role: WorkspaceRole.ADMIN,
    });

    const Wrapper = createWrapper();
    
    render(
      <Wrapper>
        <WorkspaceSettingsPage workspaceId="workspace-123" />
      </Wrapper>
    );

    // Navigate to members tab
    const membersTab = screen.getByText('Members');
    fireEvent.click(membersTab);

    await waitFor(() => {
      expect(screen.getByText('Member User')).toBeInTheDocument();
    });

    // Find and update member role
    const roleSelects = screen.getAllByDisplayValue('member');
    const memberRoleSelect = roleSelects[0]; // First member role select
    
    fireEvent.change(memberRoleSelect, { target: { value: WorkspaceRole.ADMIN } });

    await waitFor(() => {
      expect(mockCohereClient.updateWorkspaceMemberRole).toHaveBeenCalledWith({
        workspaceId: 'workspace-123',
        memberId: 'member-2',
        role: WorkspaceRole.ADMIN,
      });
    });
  });
});
