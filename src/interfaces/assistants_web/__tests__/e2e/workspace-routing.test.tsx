/**
 * @vitest-environment jsdom
 */

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { render, screen, waitFor } from '@testing-library/react';
import { useRouter } from 'next/navigation';
import { describe, it, expect, vi, beforeEach } from 'vitest';

import { CohereClientProvider, Organization } from '@/cohere-client';
import { useOrganization } from '@/hooks/use-organization';
import { useWorkspaceRoutes } from '@/hooks/use-workspace-routes';

// Mock Next.js navigation
vi.mock('next/navigation', () => ({
  useRouter: vi.fn(),
  useParams: vi.fn(() => ({ workspaceId: 'test-workspace' })),
}));

// Mock the CohereClient
const mockCohereClient = {
  listOrganizations: vi.fn(),
  setOrganizationId: vi.fn(),
  getOrganizationId: vi.fn(),
};

// Mock organization data
const mockOrganizations: Organization[] = [
  {
    id: 'workspace-1',
    name: 'Test Workspace 1',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
  {
    id: 'workspace-2',
    name: 'Test Workspace 2',
    created_at: '2024-01-02T00:00:00Z',
    updated_at: '2024-01-02T00:00:00Z',
  },
];

// Mock the useCohereClient hook
vi.mock('@/cohere-client', async () => {
  const actual = await vi.importActual('@/cohere-client');
  return {
    ...actual,
    useCohereClient: () => mockCohereClient,
  };
});

// Test component that uses workspace routing
const TestWorkspaceComponent = () => {
  const { organizations, currentOrganization, switchOrganization } = useOrganization();
  const { navigateToConversation, navigateToDiscover, workspaceId } = useWorkspaceRoutes();

  return (
    <div>
      <div data-testid="workspace-id">{workspaceId}</div>
      <div data-testid="current-org">
        {currentOrganization ? currentOrganization.name : 'No organization'}
      </div>
      <div data-testid="org-count">{organizations.length}</div>
      
      <button
        data-testid="switch-workspace"
        onClick={() => switchOrganization(mockOrganizations[1])}
      >
        Switch Workspace
      </button>
      
      <button
        data-testid="navigate-conversation"
        onClick={() => navigateToConversation('test-conv')}
      >
        Navigate to Conversation
      </button>
      
      <button
        data-testid="navigate-discover"
        onClick={() => navigateToDiscover()}
      >
        Navigate to Discover
      </button>
    </div>
  );
};

// Test wrapper
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      <CohereClientProvider client={mockCohereClient as any}>
        {children}
      </CohereClientProvider>
    </QueryClientProvider>
  );
};

describe('Workspace Routing E2E', () => {
  const mockPush = vi.fn();
  const mockRouter = { push: mockPush };

  beforeEach(() => {
    vi.clearAllMocks();
    (useRouter as any).mockReturnValue(mockRouter);
    mockCohereClient.listOrganizations.mockResolvedValue(mockOrganizations);
    
    // Reset the organization store state
    const { useOrganizationStore } = require('@/stores');
    const store = useOrganizationStore.getState();
    store.resetOrganizationState();
  });

  it('should integrate organization management with workspace routing', async () => {
    const Wrapper = createWrapper();
    
    render(
      <Wrapper>
        <TestWorkspaceComponent />
      </Wrapper>
    );

    // Wait for organizations to load
    await waitFor(() => {
      expect(screen.getByTestId('org-count')).toHaveTextContent('2');
    });

    // Should show current organization
    await waitFor(() => {
      expect(screen.getByTestId('current-org')).toHaveTextContent('Test Workspace 1');
    });

    // Should show workspace ID from params
    expect(screen.getByTestId('workspace-id')).toHaveTextContent('test-workspace');
  });

  it('should handle workspace switching and navigation', async () => {
    const Wrapper = createWrapper();
    
    render(
      <Wrapper>
        <TestWorkspaceComponent />
      </Wrapper>
    );

    // Wait for initial load
    await waitFor(() => {
      expect(screen.getByTestId('org-count')).toHaveTextContent('2');
    });

    // Switch workspace
    const switchButton = screen.getByTestId('switch-workspace');
    switchButton.click();

    await waitFor(() => {
      expect(screen.getByTestId('current-org')).toHaveTextContent('Test Workspace 2');
    });

    // Test navigation to conversation
    const navConvButton = screen.getByTestId('navigate-conversation');
    navConvButton.click();

    expect(mockPush).toHaveBeenCalledWith('/test-workspace/c/test-conv');

    // Test navigation to discover
    const navDiscoverButton = screen.getByTestId('navigate-discover');
    navDiscoverButton.click();

    expect(mockPush).toHaveBeenCalledWith('/test-workspace/discover');
  });

  it('should update CohereClient when organization changes', async () => {
    const Wrapper = createWrapper();
    
    render(
      <Wrapper>
        <TestWorkspaceComponent />
      </Wrapper>
    );

    // Wait for initial load and organization to be set
    await waitFor(() => {
      expect(mockCohereClient.setOrganizationId).toHaveBeenCalledWith('workspace-1');
    });

    // Switch workspace
    const switchButton = screen.getByTestId('switch-workspace');
    switchButton.click();

    // Should update CohereClient with new organization ID
    await waitFor(() => {
      expect(mockCohereClient.setOrganizationId).toHaveBeenCalledWith('workspace-2');
    });
  });

  it('should handle API errors gracefully', async () => {
    mockCohereClient.listOrganizations.mockRejectedValue(new Error('API Error'));
    
    const Wrapper = createWrapper();
    
    render(
      <Wrapper>
        <TestWorkspaceComponent />
      </Wrapper>
    );

    // Should show no organization when API fails
    await waitFor(() => {
      expect(screen.getByTestId('current-org')).toHaveTextContent('No organization');
    });

    expect(screen.getByTestId('org-count')).toHaveTextContent('0');
  });

  it('should maintain workspace context across navigation', async () => {
    const Wrapper = createWrapper();
    
    render(
      <Wrapper>
        <TestWorkspaceComponent />
      </Wrapper>
    );

    // Wait for initial load
    await waitFor(() => {
      expect(screen.getByTestId('workspace-id')).toHaveTextContent('test-workspace');
    });

    // Navigate to conversation
    const navButton = screen.getByTestId('navigate-conversation');
    navButton.click();

    // Should maintain workspace context in navigation
    expect(mockPush).toHaveBeenCalledWith('/test-workspace/c/test-conv');
    
    // Workspace ID should remain the same
    expect(screen.getByTestId('workspace-id')).toHaveTextContent('test-workspace');
  });
});
