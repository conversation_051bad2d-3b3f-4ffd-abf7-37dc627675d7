import { NextRequest, NextResponse } from 'next/server';

import { COOKIE_KEYS } from '@/constants';

/**
 * Middleware to handle workspace routing and redirects
 */
export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  // Skip middleware for static files, API routes, and auth routes
  if (
    pathname.startsWith('/_next') ||
    pathname.startsWith('/api') ||
    pathname.startsWith('/healthz') ||
    pathname.startsWith('/share') ||
    pathname.includes('.') ||
    pathname.startsWith('/auth') ||
    pathname.startsWith('/login') ||
    pathname.startsWith('/logout') ||
    pathname.startsWith('/register')
  ) {
    return NextResponse.next();
  }

  // Check if the path already includes a workspace ID (format: /[workspaceId]/...)
  const workspacePathRegex = /^\/[a-zA-Z0-9_-]+\/(c|a|discover|new|edit|settings)/;
  const isWorkspacePath = workspacePathRegex.test(pathname);
  
  // If it's already a workspace path, continue
  if (isWorkspacePath) {
    return NextResponse.next();
  }

  // Check for legacy paths that need to be redirected to workspace-aware paths
  const legacyPathRegex = /^\/(c|a|discover|new|edit|settings)/;
  const isLegacyPath = legacyPathRegex.test(pathname);
  
  if (isLegacyPath) {
    // Get the organization ID from cookies
    const organizationId = request.cookies.get(COOKIE_KEYS.organizationId)?.value;
    
    if (organizationId) {
      // Redirect to workspace-aware path
      const workspaceUrl = new URL(`/${organizationId}${pathname}`, request.url);
      return NextResponse.redirect(workspaceUrl);
    } else {
      // If no organization ID in cookies, redirect to root to let the app handle organization selection
      const rootUrl = new URL('/', request.url);
      return NextResponse.redirect(rootUrl);
    }
  }

  // For root path (/), check if we have an organization ID and redirect to workspace
  if (pathname === '/') {
    const organizationId = request.cookies.get(COOKIE_KEYS.organizationId)?.value;
    
    if (organizationId) {
      const workspaceUrl = new URL(`/${organizationId}`, request.url);
      return NextResponse.redirect(workspaceUrl);
    }
    // If no organization ID, let the app handle organization selection
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};
