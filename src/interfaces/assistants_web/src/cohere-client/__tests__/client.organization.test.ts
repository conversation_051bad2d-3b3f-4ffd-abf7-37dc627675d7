import { describe, it, expect, vi, beforeEach } from 'vitest';

import { CohereClient } from '../client';

// Mock fetch
const mockFetch = vi.fn();

describe('CohereClient - Organization Support', () => {
  let client: CohereClient;
  const mockHostname = 'http://localhost:8000';
  const mockAuthToken = 'test-auth-token';
  const mockOrganizationId = 'org-123';

  beforeEach(() => {
    vi.clearAllMocks();
    mockFetch.mockResolvedValue({
      ok: true,
      status: 200,
      json: () => Promise.resolve({}),
    });
  });

  describe('constructor', () => {
    it('should create client without organization ID', () => {
      client = new CohereClient({
        hostname: mockHostname,
        fetch: mockFetch,
        authToken: mockAuthToken,
      });

      expect(client.getOrganizationId()).toBeUndefined();
    });

    it('should create client with organization ID', () => {
      client = new CohereClient({
        hostname: mockHostname,
        fetch: mockFetch,
        authToken: mockAuthToken,
        organizationId: mockOrganizationId,
      });

      expect(client.getOrganizationId()).toBe(mockOrganizationId);
    });
  });

  describe('setOrganizationId', () => {
    beforeEach(() => {
      client = new CohereClient({
        hostname: mockHostname,
        fetch: mockFetch,
        authToken: mockAuthToken,
      });
    });

    it('should set organization ID', () => {
      expect(client.getOrganizationId()).toBeUndefined();
      
      client.setOrganizationId(mockOrganizationId);
      
      expect(client.getOrganizationId()).toBe(mockOrganizationId);
    });

    it('should clear organization ID when set to undefined', () => {
      client.setOrganizationId(mockOrganizationId);
      expect(client.getOrganizationId()).toBe(mockOrganizationId);
      
      client.setOrganizationId(undefined);
      
      expect(client.getOrganizationId()).toBeUndefined();
    });

    it('should update organization ID when changed', () => {
      const newOrganizationId = 'org-456';
      
      client.setOrganizationId(mockOrganizationId);
      expect(client.getOrganizationId()).toBe(mockOrganizationId);
      
      client.setOrganizationId(newOrganizationId);
      
      expect(client.getOrganizationId()).toBe(newOrganizationId);
    });
  });

  describe('getOrganizationId', () => {
    it('should return undefined when no organization ID is set', () => {
      client = new CohereClient({
        hostname: mockHostname,
        fetch: mockFetch,
        authToken: mockAuthToken,
      });

      expect(client.getOrganizationId()).toBeUndefined();
    });

    it('should return organization ID when set in constructor', () => {
      client = new CohereClient({
        hostname: mockHostname,
        fetch: mockFetch,
        authToken: mockAuthToken,
        organizationId: mockOrganizationId,
      });

      expect(client.getOrganizationId()).toBe(mockOrganizationId);
    });

    it('should return updated organization ID after setOrganizationId call', () => {
      client = new CohereClient({
        hostname: mockHostname,
        fetch: mockFetch,
        authToken: mockAuthToken,
      });

      client.setOrganizationId(mockOrganizationId);
      
      expect(client.getOrganizationId()).toBe(mockOrganizationId);
    });
  });

  describe('headers with organization ID', () => {
    it('should include Organization-Id header when organization ID is set', () => {
      client = new CohereClient({
        hostname: mockHostname,
        fetch: mockFetch,
        authToken: mockAuthToken,
        organizationId: mockOrganizationId,
      });

      // Access the private getHeaders method through reflection for testing
      const headers = (client as any).getHeaders();
      
      expect(headers['Organization-Id']).toBe(mockOrganizationId);
    });

    it('should not include Organization-Id header when organization ID is not set', () => {
      client = new CohereClient({
        hostname: mockHostname,
        fetch: mockFetch,
        authToken: mockAuthToken,
      });

      // Access the private getHeaders method through reflection for testing
      const headers = (client as any).getHeaders();
      
      expect(headers['Organization-Id']).toBeUndefined();
    });

    it('should update Organization-Id header when organization ID is changed', () => {
      client = new CohereClient({
        hostname: mockHostname,
        fetch: mockFetch,
        authToken: mockAuthToken,
      });

      // Initially no Organization-Id header
      let headers = (client as any).getHeaders();
      expect(headers['Organization-Id']).toBeUndefined();

      // Set organization ID
      client.setOrganizationId(mockOrganizationId);
      headers = (client as any).getHeaders();
      expect(headers['Organization-Id']).toBe(mockOrganizationId);

      // Clear organization ID
      client.setOrganizationId(undefined);
      headers = (client as any).getHeaders();
      expect(headers['Organization-Id']).toBeUndefined();
    });

    it('should include all expected headers including Organization-Id', () => {
      client = new CohereClient({
        hostname: mockHostname,
        fetch: mockFetch,
        authToken: mockAuthToken,
        organizationId: mockOrganizationId,
      });

      const headers = (client as any).getHeaders();
      
      expect(headers['Content-Type']).toBe('application/json');
      expect(headers['Authorization']).toBe(`Bearer ${mockAuthToken}`);
      expect(headers['Organization-Id']).toBe(mockOrganizationId);
      expect(headers['User-Id']).toBe('user-id');
      expect(headers['Connection']).toBe('keep-alive');
      expect(headers['X-Date']).toBeDefined();
    });
  });
});
