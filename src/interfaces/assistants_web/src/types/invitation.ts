/**
 * Types for workspace invitation system
 */

export interface WorkspaceInvitation {
  id: string;
  workspace_id: string;
  workspace_name: string;
  inviter_email: string;
  inviter_name: string;
  invitee_email: string;
  role: WorkspaceRole;
  status: InvitationStatus;
  token: string;
  expires_at: string;
  created_at: string;
  updated_at: string;
}

export enum WorkspaceRole {
  ADMIN = 'admin',
  MEMBER = 'member',
  VIEWER = 'viewer',
}

export enum InvitationStatus {
  PENDING = 'pending',
  ACCEPTED = 'accepted',
  DECLINED = 'declined',
  EXPIRED = 'expired',
  REVOKED = 'revoked',
}

export interface CreateInvitationRequest {
  workspace_id: string;
  invitee_email: string;
  role: WorkspaceRole;
  message?: string;
}

export interface AcceptInvitationRequest {
  token: string;
}

export interface WorkspaceMember {
  id: string;
  user_id: string;
  workspace_id: string;
  email: string;
  name: string;
  role: WorkspaceRole;
  joined_at: string;
  last_active_at?: string;
}

export interface WorkspaceSettings {
  id: string;
  name: string;
  description?: string;
  allow_member_invites: boolean;
  max_members?: number;
  billing_email: string;
  created_at: string;
  updated_at: string;
}

export interface BillingInfo {
  workspace_id: string;
  stripe_customer_id: string;
  stripe_subscription_id?: string;
  plan_type: BillingPlan;
  seat_count: number;
  billing_cycle: BillingCycle;
  amount_per_seat: number;
  currency: string;
  next_billing_date?: string;
  status: BillingStatus;
}

export enum BillingPlan {
  STARTER = 'starter',
  PROFESSIONAL = 'professional',
  ENTERPRISE = 'enterprise',
}

export enum BillingCycle {
  MONTHLY = 'monthly',
  YEARLY = 'yearly',
}

export enum BillingStatus {
  ACTIVE = 'active',
  PAST_DUE = 'past_due',
  CANCELED = 'canceled',
  INCOMPLETE = 'incomplete',
  INCOMPLETE_EXPIRED = 'incomplete_expired',
  TRIALING = 'trialing',
  UNPAID = 'unpaid',
}
