import { HydrationBoundary, QueryClient, dehydrate } from '@tanstack/react-query';
import { NextPage } from 'next';

import Chat from '@/app/(main)/(chat)/Chat';
import { DEFAULT_AGENT_ID } from '@/constants';
import { getCohereServerClient } from '@/server/cohereServerClient';

type Props = {
  params: {
    workspaceId: string;
    conversationId: string;
  };
  searchParams: Record<string, string>;
};

/**
 * Workspace-aware conversation page
 */
const WorkspaceConversationPage: NextPage<Props> = async ({ params }) => {
  const { workspaceId, conversationId } = params;
  const cohereServerClient = getCohereServerClient(workspaceId);
  const queryClient = new QueryClient();

  await Promise.all([
    queryClient.prefetchQuery({
      queryKey: ['conversation', conversationId],
      queryFn: async () =>
        cohereServerClient.getConversation({ conversationId }),
    }),
    queryClient.prefetchQuery({
      queryKey: ['agent', DEFAULT_AGENT_ID],
      queryFn: () => cohereServerClient.getDefaultAgent(),
    }),
  ]);

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <Chat conversationId={conversationId} />
    </HydrationBoundary>
  );
};

export default WorkspaceConversationPage;
