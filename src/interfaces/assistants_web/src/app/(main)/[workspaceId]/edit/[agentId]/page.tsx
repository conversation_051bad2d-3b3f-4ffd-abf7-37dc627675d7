import { NextPage } from 'next';

import { UpdateAgent } from '@/app/(main)/edit/[agentId]/UpdateAgent';
import { getCohereServerClient } from '@/server/cohereServerClient';

type Props = {
  params: {
    workspaceId: string;
    agentId: string;
  };
};

/**
 * Workspace-aware agent editing page
 */
const WorkspaceEditAgentPage: NextPage<Props> = async ({ params }) => {
  const { workspaceId, agentId } = params;
  const cohereServerClient = getCohereServerClient(workspaceId);

  const agent = await cohereServerClient.getAgent(agentId);

  return <UpdateAgent agent={agent} />;
};

export default WorkspaceEditAgentPage;
