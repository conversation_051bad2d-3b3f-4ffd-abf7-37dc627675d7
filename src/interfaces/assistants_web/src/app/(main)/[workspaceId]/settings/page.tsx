import { HydrationBoundary, QueryClient, dehydrate } from '@tanstack/react-query';
import { NextPage } from 'next';
import { notFound } from 'next/navigation';

import { getCohereServerClient } from '@/server/cohereServerClient';

import { WorkspaceSettingsPage } from './WorkspaceSettingsPage';

type Props = {
  params: {
    workspaceId: string;
  };
};

/**
 * Workspace settings page with server-side data prefetching
 */
const SettingsPage: NextPage<Props> = async ({ params }) => {
  const { workspaceId } = params;
  const cohereServerClient = getCohereServerClient();
  const queryClient = new QueryClient();

  try {
    // Verify user has access to this workspace
    await queryClient.prefetchQuery({
      queryKey: ['organizations'],
      queryFn: () => cohereServerClient.listOrganizations(),
    });

    // Prefetch workspace members
    await queryClient.prefetchQuery({
      queryKey: ['workspace-members', workspaceId],
      queryFn: () => cohereServerClient.getWorkspaceMembers({ workspaceId }),
    });

    // Prefetch billing information
    await queryClient.prefetchQuery({
      queryKey: ['billing-info', workspaceId],
      queryFn: () => cohereServerClient.getBillingInfo({ workspaceId }),
    });

    // Prefetch workspace settings
    await queryClient.prefetchQuery({
      queryKey: ['workspace-settings', workspaceId],
      queryFn: () => cohereServerClient.getWorkspaceSettings({ workspaceId }),
    });
  } catch (error) {
    // If user doesn't have access to workspace, show 404
    notFound();
  }

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <WorkspaceSettingsPage workspaceId={workspaceId} />
    </HydrationBoundary>
  );
};

export default SettingsPage;
