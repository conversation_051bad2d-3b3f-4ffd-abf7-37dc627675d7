'use client';

import React, { useState } from 'react';

import { Button, Text } from '@/components/UI';
import { BillingOverview } from '@/components/Billing/BillingOverview';
import { SeatManagementModal } from '@/components/Billing/SeatManagement';
import { WorkspaceMembers } from '@/components/WorkspaceInvitations/WorkspaceMembers';
import { Settings } from '@/app/(main)/settings/Settings';

interface Props {
  workspaceId: string;
}

type SettingsTab = 'general' | 'members' | 'billing';

export const WorkspaceSettingsPage: React.FC<Props> = ({ workspaceId }) => {
  const [activeTab, setActiveTab] = useState<SettingsTab>('general');
  const [isSeatModalOpen, setIsSeatModalOpen] = useState(false);

  const tabs = [
    { id: 'general' as const, label: 'General', description: 'General application settings' },
    { id: 'members' as const, label: 'Members', description: 'Manage workspace members and invitations' },
    { id: 'billing' as const, label: 'Billing', description: 'Manage subscription and billing' },
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'general':
        return <Settings />;
      case 'members':
        return <WorkspaceMembers />;
      case 'billing':
        return (
          <div className="space-y-6">
            <BillingOverview />
            <div className="flex justify-end">
              <Button onClick={() => setIsSeatModalOpen(true)}>
                Manage Seats
              </Button>
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-8">
        <Text as="h1" className="text-3xl font-bold text-gray-900">
          Workspace Settings
        </Text>
        <Text className="text-gray-600 mt-2">
          Manage your workspace configuration, members, and billing
        </Text>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200 mb-8">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="min-h-[600px]">
        {renderTabContent()}
      </div>

      {/* Modals */}
      <SeatManagementModal
        isOpen={isSeatModalOpen}
        onClose={() => setIsSeatModalOpen(false)}
      />
    </div>
  );
};
