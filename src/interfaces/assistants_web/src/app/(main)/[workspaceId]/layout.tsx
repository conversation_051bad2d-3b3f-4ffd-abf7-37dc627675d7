import { NextPage } from 'next';
import { notFound } from 'next/navigation';

import { getCohereServerClient } from '@/server/cohereServerClient';

type Props = {
  children: React.ReactNode;
  params: {
    workspaceId: string;
  };
};

/**
 * Workspace layout that validates workspace access and sets up workspace context
 */
const WorkspaceLayout: NextPage<Props> = async ({ children, params }) => {
  const { workspaceId } = params;
  
  // Validate workspace access
  const cohereServerClient = getCohereServerClient(workspaceId);
  
  try {
    // Check if the user has access to this workspace
    const organizations = await cohereServerClient.listOrganizations();
    const hasAccess = organizations.some(org => org.id === workspaceId);
    
    if (!hasAccess) {
      notFound();
    }
  } catch (error) {
    // If we can't fetch organizations or validate access, show not found
    notFound();
  }

  return <>{children}</>;
};

export default WorkspaceLayout;
