import { HydrationBoundary, QueryClient, dehydrate } from '@tanstack/react-query';
import { NextPage } from 'next';

import Chat from '@/app/(main)/(chat)/Chat';
import { getCohereServerClient } from '@/server/cohereServerClient';

type Props = {
  params: {
    workspaceId: string;
    agentId: string;
  };
  searchParams: Record<string, string>;
};

/**
 * Workspace-aware agent page - shows new chat with specific agent
 */
const WorkspaceAgentPage: NextPage<Props> = async ({ params }) => {
  const { workspaceId, agentId } = params;
  const cohereServerClient = getCohereServerClient(workspaceId);
  const queryClient = new QueryClient();

  await queryClient.prefetchQuery({
    queryKey: ['agent', agentId],
    queryFn: () => cohereServerClient.getAgent(agentId),
  });

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <Chat agentId={agentId} />
    </HydrationBoundary>
  );
};

export default WorkspaceAgentPage;
