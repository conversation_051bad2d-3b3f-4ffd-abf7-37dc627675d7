import { HydrationBoundary, QueryClient, dehydrate } from '@tanstack/react-query';
import { NextPage } from 'next';

import Chat from '@/app/(main)/(chat)/Chat';
import { getCohereServerClient } from '@/server/cohereServerClient';

type Props = {
  params: {
    workspaceId: string;
    agentId: string;
    conversationId: string;
  };
  searchParams: Record<string, string>;
};

/**
 * Workspace-aware agent conversation page
 */
const WorkspaceAgentConversationPage: NextPage<Props> = async ({ params }) => {
  const { workspaceId, agentId, conversationId } = params;
  const cohereServerClient = getCohereServerClient(workspaceId);
  const queryClient = new QueryClient();

  await Promise.all([
    queryClient.prefetchQuery({
      queryKey: ['conversation', conversationId],
      queryFn: async () =>
        cohereServerClient.getConversation({ conversationId }),
    }),
    queryClient.prefetchQuery({
      queryKey: ['agent', agentId],
      queryFn: () => cohereServerClient.getAgent(agentId),
    }),
  ]);

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <Chat conversationId={conversationId} agentId={agentId} />
    </HydrationBoundary>
  );
};

export default WorkspaceAgentConversationPage;
