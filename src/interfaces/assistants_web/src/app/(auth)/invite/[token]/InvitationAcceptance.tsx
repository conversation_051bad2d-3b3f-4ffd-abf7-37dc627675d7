'use client';

import { useMutation, useQuery } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import React, { useState } from 'react';

import { <PERSON><PERSON>, Spinner, Text } from '@/components/UI';
import { useCohereClient } from '@/cohere-client';
import { InvitationStatus } from '@/types/invitation';

interface Props {
  token: string;
}

export const InvitationAcceptance: React.FC<Props> = ({ token }) => {
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const cohereClient = useCohereClient();

  const {
    data: invitation,
    isLoading,
    error: fetchError,
  } = useQuery({
    queryKey: ['invitation', token],
    queryFn: () => cohereClient.getInvitation({ token }),
  });

  const acceptInvitationMutation = useMutation({
    mutationFn: () => cohereClient.acceptInvitation({ token }),
    onSuccess: (data) => {
      // Redirect to the workspace
      router.push(`/${data.workspace_id}`);
    },
    onError: (error: any) => {
      setError(error.message || 'Failed to accept invitation');
    },
  });

  const declineInvitationMutation = useMutation({
    mutationFn: () => cohereClient.declineInvitation({ token }),
    onSuccess: () => {
      router.push('/');
    },
    onError: (error: any) => {
      setError(error.message || 'Failed to decline invitation');
    },
  });

  const handleAccept = () => {
    setError(null);
    acceptInvitationMutation.mutate();
  };

  const handleDecline = () => {
    setError(null);
    declineInvitationMutation.mutate();
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Spinner className="mx-auto mb-4" />
          <Text>Loading invitation...</Text>
        </div>
      </div>
    );
  }

  if (fetchError || !invitation) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="max-w-md mx-auto text-center">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6">
            <Text className="text-red-800 font-medium mb-2">
              Invalid Invitation
            </Text>
            <Text className="text-red-600 mb-4">
              This invitation link is invalid, expired, or has already been used.
            </Text>
            <Button onClick={() => router.push('/')}>
              Go to Home
            </Button>
          </div>
        </div>
      </div>
    );
  }

  if (invitation.status !== InvitationStatus.PENDING) {
    const statusMessages = {
      [InvitationStatus.ACCEPTED]: 'This invitation has already been accepted.',
      [InvitationStatus.DECLINED]: 'This invitation has been declined.',
      [InvitationStatus.EXPIRED]: 'This invitation has expired.',
      [InvitationStatus.REVOKED]: 'This invitation has been revoked.',
    };

    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="max-w-md mx-auto text-center">
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
            <Text className="text-yellow-800 font-medium mb-2">
              Invitation Not Available
            </Text>
            <Text className="text-yellow-600 mb-4">
              {statusMessages[invitation.status]}
            </Text>
            <Button onClick={() => router.push('/')}>
              Go to Home
            </Button>
          </div>
        </div>
      </div>
    );
  }

  const isExpired = new Date(invitation.expires_at) < new Date();
  if (isExpired) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="max-w-md mx-auto text-center">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6">
            <Text className="text-red-800 font-medium mb-2">
              Invitation Expired
            </Text>
            <Text className="text-red-600 mb-4">
              This invitation expired on {new Date(invitation.expires_at).toLocaleDateString()}.
              Please contact the workspace administrator for a new invitation.
            </Text>
            <Button onClick={() => router.push('/')}>
              Go to Home
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <div className="text-center mb-6">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Text className="text-blue-600 text-2xl font-bold">
                {invitation.workspace_name.charAt(0).toUpperCase()}
              </Text>
            </div>
            <Text as="h1" className="text-2xl font-bold text-gray-900 mb-2">
              You&apos;re Invited!
            </Text>
            <Text className="text-gray-600">
              {invitation.inviter_name || invitation.inviter_email} has invited you to join
            </Text>
          </div>

          <div className="bg-gray-50 rounded-lg p-4 mb-6">
            <Text className="font-semibold text-lg text-center">
              {invitation.workspace_name}
            </Text>
            <Text className="text-gray-600 text-center mt-1">
              as a {invitation.role}
            </Text>
          </div>

          <div className="space-y-4 mb-6">
            <div>
              <Text className="text-sm font-medium text-gray-700">Invited by:</Text>
              <Text className="text-gray-900">
                {invitation.inviter_name || invitation.inviter_email}
              </Text>
            </div>
            <div>
              <Text className="text-sm font-medium text-gray-700">Your email:</Text>
              <Text className="text-gray-900">{invitation.invitee_email}</Text>
            </div>
            <div>
              <Text className="text-sm font-medium text-gray-700">Expires:</Text>
              <Text className="text-gray-900">
                {new Date(invitation.expires_at).toLocaleDateString()}
              </Text>
            </div>
          </div>

          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
              <Text className="text-red-800 text-sm">{error}</Text>
            </div>
          )}

          <div className="flex space-x-3">
            <Button
              onClick={handleDecline}
              kind="secondary"
              className="flex-1"
              disabled={acceptInvitationMutation.isPending || declineInvitationMutation.isPending}
            >
              Decline
            </Button>
            <Button
              onClick={handleAccept}
              className="flex-1"
              isLoading={acceptInvitationMutation.isPending}
              disabled={declineInvitationMutation.isPending}
            >
              Accept Invitation
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
