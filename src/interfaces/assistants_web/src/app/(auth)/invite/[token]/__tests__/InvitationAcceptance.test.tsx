import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { useRouter } from 'next/navigation';
import { describe, it, expect, vi, beforeEach } from 'vitest';

import { CohereClientProvider } from '@/cohere-client';
import { InvitationStatus, WorkspaceInvitation, WorkspaceRole } from '@/types/invitation';

import { InvitationAcceptance } from '../InvitationAcceptance';

// Mock Next.js router
vi.mock('next/navigation', () => ({
  useRouter: vi.fn(),
}));

// Mock the CohereClient
const mockCohereClient = {
  getInvitation: vi.fn(),
  acceptInvitation: vi.fn(),
  declineInvitation: vi.fn(),
};

// Mock invitation data
const mockInvitation: WorkspaceInvitation = {
  id: 'invitation-123',
  workspace_id: 'workspace-456',
  workspace_name: 'Test Workspace',
  inviter_email: '<EMAIL>',
  inviter_name: 'Admin User',
  invitee_email: '<EMAIL>',
  role: WorkspaceRole.MEMBER,
  status: InvitationStatus.PENDING,
  token: 'test-token',
  expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
};

// Mock the useCohereClient hook
vi.mock('@/cohere-client', async () => {
  const actual = await vi.importActual('@/cohere-client');
  return {
    ...actual,
    useCohereClient: () => mockCohereClient,
  };
});

// Test wrapper
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

  const TestWrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      <CohereClientProvider client={mockCohereClient as any}>
        {children}
      </CohereClientProvider>
    </QueryClientProvider>
  );

  return TestWrapper;
};

describe('InvitationAcceptance', () => {
  const mockPush = vi.fn();
  const mockRouter = { push: mockPush };

  beforeEach(() => {
    vi.clearAllMocks();
    (useRouter as any).mockReturnValue(mockRouter);
  });

  it('should render invitation details correctly', async () => {
    mockCohereClient.getInvitation.mockResolvedValue(mockInvitation);

    const Wrapper = createWrapper();
    
    render(
      <Wrapper>
        <InvitationAcceptance token="test-token" />
      </Wrapper>
    );

    await waitFor(() => {
      expect(screen.getByText("You're Invited!")).toBeInTheDocument();
    });

    expect(screen.getByText('Test Workspace')).toBeInTheDocument();
    expect(screen.getByText('as a member')).toBeInTheDocument();
    expect(screen.getByText('Admin User')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });

  it('should handle accepting invitation', async () => {
    mockCohereClient.getInvitation.mockResolvedValue(mockInvitation);
    mockCohereClient.acceptInvitation.mockResolvedValue({
      workspace_id: 'workspace-456',
    });

    const Wrapper = createWrapper();
    
    render(
      <Wrapper>
        <InvitationAcceptance token="test-token" />
      </Wrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Accept Invitation')).toBeInTheDocument();
    });

    const acceptButton = screen.getByText('Accept Invitation');
    fireEvent.click(acceptButton);

    await waitFor(() => {
      expect(mockCohereClient.acceptInvitation).toHaveBeenCalledWith({
        token: 'test-token',
      });
    });

    await waitFor(() => {
      expect(mockPush).toHaveBeenCalledWith('/workspace-456');
    });
  });

  it('should handle declining invitation', async () => {
    mockCohereClient.getInvitation.mockResolvedValue(mockInvitation);
    mockCohereClient.declineInvitation.mockResolvedValue({});

    const Wrapper = createWrapper();
    
    render(
      <Wrapper>
        <InvitationAcceptance token="test-token" />
      </Wrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Decline')).toBeInTheDocument();
    });

    const declineButton = screen.getByText('Decline');
    fireEvent.click(declineButton);

    await waitFor(() => {
      expect(mockCohereClient.declineInvitation).toHaveBeenCalledWith({
        token: 'test-token',
      });
    });

    await waitFor(() => {
      expect(mockPush).toHaveBeenCalledWith('/');
    });
  });

  it('should show error for invalid invitation', async () => {
    mockCohereClient.getInvitation.mockRejectedValue(new Error('Invitation not found'));

    const Wrapper = createWrapper();
    
    render(
      <Wrapper>
        <InvitationAcceptance token="invalid-token" />
      </Wrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Invalid Invitation')).toBeInTheDocument();
    });

    expect(screen.getByText('This invitation link is invalid, expired, or has already been used.')).toBeInTheDocument();
  });

  it('should show message for already accepted invitation', async () => {
    const acceptedInvitation = {
      ...mockInvitation,
      status: InvitationStatus.ACCEPTED,
    };
    mockCohereClient.getInvitation.mockResolvedValue(acceptedInvitation);

    const Wrapper = createWrapper();
    
    render(
      <Wrapper>
        <InvitationAcceptance token="test-token" />
      </Wrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Invitation Not Available')).toBeInTheDocument();
    });

    expect(screen.getByText('This invitation has already been accepted.')).toBeInTheDocument();
  });

  it('should show message for expired invitation', async () => {
    const expiredInvitation = {
      ...mockInvitation,
      expires_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
    };
    mockCohereClient.getInvitation.mockResolvedValue(expiredInvitation);

    const Wrapper = createWrapper();
    
    render(
      <Wrapper>
        <InvitationAcceptance token="test-token" />
      </Wrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Invitation Expired')).toBeInTheDocument();
    });

    expect(screen.getByText(/This invitation expired on/)).toBeInTheDocument();
  });

  it('should handle API errors during acceptance', async () => {
    mockCohereClient.getInvitation.mockResolvedValue(mockInvitation);
    mockCohereClient.acceptInvitation.mockRejectedValue(new Error('Server error'));

    const Wrapper = createWrapper();
    
    render(
      <Wrapper>
        <InvitationAcceptance token="test-token" />
      </Wrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Accept Invitation')).toBeInTheDocument();
    });

    const acceptButton = screen.getByText('Accept Invitation');
    fireEvent.click(acceptButton);

    await waitFor(() => {
      expect(screen.getByText('Server error')).toBeInTheDocument();
    });

    expect(mockPush).not.toHaveBeenCalled();
  });

  it('should show loading state', () => {
    mockCohereClient.getInvitation.mockImplementation(() => new Promise(() => {})); // Never resolves

    const Wrapper = createWrapper();
    
    render(
      <Wrapper>
        <InvitationAcceptance token="test-token" />
      </Wrapper>
    );

    expect(screen.getByText('Loading invitation...')).toBeInTheDocument();
  });
});
