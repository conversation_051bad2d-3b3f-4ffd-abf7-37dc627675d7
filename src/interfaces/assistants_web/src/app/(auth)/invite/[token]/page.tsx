import { HydrationBoundary, QueryClient, dehydrate } from '@tanstack/react-query';
import { NextPage } from 'next';
import { notFound } from 'next/navigation';

import { getCohereServerClient } from '@/server/cohereServerClient';

import { InvitationAcceptance } from './InvitationAcceptance';

type Props = {
  params: {
    token: string;
  };
};

/**
 * Invitation acceptance page
 */
const InvitePage: NextPage<Props> = async ({ params }) => {
  const { token } = params;
  const cohereServerClient = getCohereServerClient();
  const queryClient = new QueryClient();

  try {
    // Prefetch invitation details
    await queryClient.prefetchQuery({
      queryKey: ['invitation', token],
      queryFn: () => cohereServerClient.getInvitation({ token }),
    });
  } catch (error) {
    // If invitation is not found or invalid, show 404
    notFound();
  }

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <InvitationAcceptance token={token} />
    </HydrationBoundary>
  );
};

export default InvitePage;
