'use server';

import { cookies } from 'next/headers';

import { COOKIE_KEYS } from '@/constants';

/**
 * Set the current organization ID in cookies
 */
export async function setOrganizationId(organizationId: string) {
  const cookieStore = cookies();
  cookieStore.set(COOKIE_KEYS.organizationId, organizationId, {
    httpOnly: true,
    secure: true, // Always use secure cookies
    sameSite: 'lax',
    maxAge: 60 * 60 * 24 * 30, // 30 days
  });
}

/**
 * Clear the current organization ID from cookies
 */
export async function clearOrganizationId() {
  const cookieStore = cookies();
  cookieStore.delete(COOKIE_KEYS.organizationId);
}

/**
 * Get the current organization ID from cookies
 */
export async function getOrganizationId(): Promise<string | undefined> {
  const cookieStore = cookies();
  return cookieStore.get(COOKIE_KEYS.organizationId)?.value;
}
