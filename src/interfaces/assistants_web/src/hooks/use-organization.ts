'use client';

import { useQuery } from '@tanstack/react-query';
import { useEffect } from 'react';

import { Organization, useCohereClient } from '@/cohere-client';
import { setOrganizationId, clearOrganizationId } from '@/server/actions/organization';
import { useOrganizationStore } from '@/stores';

/**
 * Hook for managing organization context and API client configuration
 */
export const useOrganization = () => {
  const cohereClient = useCohereClient();
  const {
    currentOrganization,
    organizations,
    isLoading,
    error,
    setCurrentOrganization,
    setOrganizations,
    setLoading,
    setError,
    resetOrganizationState,
  } = useOrganizationStore();

  // Fetch user's organizations
  const {
    data: fetchedOrganizations,
    isLoading: isFetchingOrganizations,
    error: fetchError,
    refetch: refetchOrganizations,
  } = useQuery({
    queryKey: ['organizations'],
    queryFn: () => cohereClient.listOrganizations(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Update store when organizations are fetched
  useEffect(() => {
    if (fetchedOrganizations) {
      setOrganizations(fetchedOrganizations);
      
      // If no current organization is set and we have organizations, set the first one
      if (!currentOrganization && fetchedOrganizations.length > 0) {
        setCurrentOrganization(fetchedOrganizations[0]);
      }
    }
  }, [fetchedOrganizations, currentOrganization, setOrganizations, setCurrentOrganization]);

  // Update loading state
  useEffect(() => {
    setLoading(isFetchingOrganizations);
  }, [isFetchingOrganizations, setLoading]);

  // Update error state
  useEffect(() => {
    if (fetchError) {
      setError(fetchError.message || 'Failed to fetch organizations');
    } else {
      setError(null);
    }
  }, [fetchError, setError]);

  // Update CohereClient organization ID when current organization changes
  useEffect(() => {
    cohereClient.setOrganizationId(currentOrganization?.id);
  }, [currentOrganization, cohereClient]);

  /**
   * Switch to a different organization
   */
  const switchOrganization = async (organization: Organization | null) => {
    setCurrentOrganization(organization);

    // Update cookies
    if (organization) {
      await setOrganizationId(organization.id);
    } else {
      await clearOrganizationId();
    }
  };

  /**
   * Get organization by ID
   */
  const getOrganizationById = (id: string): Organization | undefined => {
    return organizations.find(org => org.id === id);
  };

  /**
   * Check if user has access to a specific organization
   */
  const hasAccessToOrganization = (organizationId: string): boolean => {
    return organizations.some(org => org.id === organizationId);
  };

  return {
    // State
    currentOrganization,
    organizations,
    isLoading,
    error,
    
    // Actions
    switchOrganization,
    refetchOrganizations,
    resetOrganizationState,
    
    // Utilities
    getOrganizationById,
    hasAccessToOrganization,
    
    // Computed
    hasOrganizations: organizations.length > 0,
    currentOrganizationId: currentOrganization?.id,
  };
};

/**
 * Hook for getting the current organization context
 * This is a lighter version that doesn't trigger API calls
 */
export const useCurrentOrganization = () => {
  const { currentOrganization } = useOrganizationStore();

  return {
    currentOrganization,
    currentOrganizationId: currentOrganization?.id,
  };
};
