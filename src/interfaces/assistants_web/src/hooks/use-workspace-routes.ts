'use client';

import { usePara<PERSON>, useRouter } from 'next/navigation';
import { useCallback } from 'react';

import { useCurrentOrganization } from './use-organization';

/**
 * Hook for workspace-aware routing and navigation
 */
export const useWorkspaceRoutes = () => {
  const router = useRouter();
  const params = useParams();
  const { currentOrganizationId } = useCurrentOrganization();

  // Get current workspace ID from URL params or current organization
  const workspaceId = (params?.workspaceId as string) || currentOrganizationId;

  /**
   * Navigate to a conversation within the current workspace
   */
  const navigateToConversation = useCallback(
    (conversationId: string, agentId?: string) => {
      if (!workspaceId) {
        console.warn('No workspace ID available for navigation');
        return;
      }

      const route = agentId
        ? `/${workspaceId}/a/${agentId}/c/${conversationId}`
        : `/${workspaceId}/c/${conversationId}`;
      
      router.push(route);
    },
    [router, workspaceId]
  );

  /**
   * Navigate to a new chat within the current workspace
   */
  const navigateToNewChat = useCallback(
    (agentId?: string) => {
      if (!workspaceId) {
        console.warn('No workspace ID available for navigation');
        return;
      }

      const route = agentId
        ? `/${workspaceId}/a/${agentId}`
        : `/${workspaceId}`;
      
      router.push(route);
    },
    [router, workspaceId]
  );

  /**
   * Navigate to agent editing within the current workspace
   */
  const navigateToEditAgent = useCallback(
    (agentId: string) => {
      if (!workspaceId) {
        console.warn('No workspace ID available for navigation');
        return;
      }

      router.push(`/${workspaceId}/edit/${agentId}`);
    },
    [router, workspaceId]
  );

  /**
   * Navigate to agent creation within the current workspace
   */
  const navigateToCreateAgent = useCallback(() => {
    if (!workspaceId) {
      console.warn('No workspace ID available for navigation');
      return;
    }

    router.push(`/${workspaceId}/new`);
  }, [router, workspaceId]);

  /**
   * Navigate to discover page within the current workspace
   */
  const navigateToDiscover = useCallback(() => {
    if (!workspaceId) {
      console.warn('No workspace ID available for navigation');
      return;
    }

    router.push(`/${workspaceId}/discover`);
  }, [router, workspaceId]);

  /**
   * Navigate to settings within the current workspace
   */
  const navigateToSettings = useCallback(() => {
    if (!workspaceId) {
      console.warn('No workspace ID available for navigation');
      return;
    }

    router.push(`/${workspaceId}/settings`);
  }, [router, workspaceId]);

  /**
   * Switch to a different workspace
   */
  const switchWorkspace = useCallback(
    (newWorkspaceId: string, preservePath = false) => {
      if (preservePath && params) {
        // Try to preserve the current path structure in the new workspace
        const currentPath = window.location.pathname;
        const pathWithoutWorkspace = currentPath.replace(`/${workspaceId}`, '');
        router.push(`/${newWorkspaceId}${pathWithoutWorkspace}`);
      } else {
        // Navigate to workspace root
        router.push(`/${newWorkspaceId}`);
      }
    },
    [router, workspaceId, params]
  );

  /**
   * Get workspace-aware URL for a conversation
   */
  const getConversationUrl = useCallback(
    (conversationId: string, agentId?: string) => {
      if (!workspaceId) return null;

      return agentId
        ? `/${workspaceId}/a/${agentId}/c/${conversationId}`
        : `/${workspaceId}/c/${conversationId}`;
    },
    [workspaceId]
  );

  /**
   * Check if current route is workspace-aware
   */
  const isWorkspaceRoute = Boolean(params?.workspaceId);

  /**
   * Check if we're in a specific workspace
   */
  const isInWorkspace = (targetWorkspaceId: string) => {
    return workspaceId === targetWorkspaceId;
  };

  return {
    // Current state
    workspaceId,
    isWorkspaceRoute,
    
    // Navigation functions
    navigateToConversation,
    navigateToNewChat,
    navigateToEditAgent,
    navigateToCreateAgent,
    navigateToDiscover,
    navigateToSettings,
    switchWorkspace,
    
    // Utility functions
    getConversationUrl,
    isInWorkspace,
  };
};

/**
 * Hook for getting workspace-aware URLs without navigation
 */
export const useWorkspaceUrls = () => {
  const { currentOrganizationId } = useCurrentOrganization();
  const params = useParams();
  const workspaceId = (params?.workspaceId as string) || currentOrganizationId;

  const getConversationUrl = useCallback(
    (conversationId: string, agentId?: string) => {
      if (!workspaceId) return null;

      return agentId
        ? `/${workspaceId}/a/${agentId}/c/${conversationId}`
        : `/${workspaceId}/c/${conversationId}`;
    },
    [workspaceId]
  );

  const getAgentUrl = useCallback(
    (agentId: string) => {
      if (!workspaceId) return null;
      return `/${workspaceId}/a/${agentId}`;
    },
    [workspaceId]
  );

  const getEditAgentUrl = useCallback(
    (agentId: string) => {
      if (!workspaceId) return null;
      return `/${workspaceId}/edit/${agentId}`;
    },
    [workspaceId]
  );

  return {
    workspaceId,
    getConversationUrl,
    getAgentUrl,
    getEditAgentUrl,
  };
};
