import { QueryClient, QueryClientProvider, useQuery } from '@tanstack/react-query';
import { renderHook, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';

import { CohereClientProvider, Organization } from '@/cohere-client';
import { useOrganizationStore } from '@/stores';
import { useOrganization, useCurrentOrganization } from '../use-organization';

// Create mocks using vi.hoisted to ensure they're available during module mocking
const { mockUseQuery } = vi.hoisted(() => ({
  mockUseQuery: vi.fn(),
}));

// Mock the CohereClient
const mockCohereClient = {
  listOrganizations: vi.fn(),
  setOrganizationId: vi.fn(),
  getOrganizationId: vi.fn(),
};

// Mock organization data
const mockOrganizations: Organization[] = [
  {
    id: 'org-1',
    name: 'Test Organization 1',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
  {
    id: 'org-2',
    name: 'Test Organization 2',
    created_at: '2024-01-02T00:00:00Z',
    updated_at: '2024-01-02T00:00:00Z',
  },
];

// Mock the useCohereClient hook
vi.mock('@/cohere-client', async () => {
  const actual = await vi.importActual('@/cohere-client');
  return {
    ...actual,
    useCohereClient: () => mockCohereClient,
  };
});

// Mock React Query
vi.mock('@tanstack/react-query', async () => {
  const actual = await vi.importActual('@tanstack/react-query');
  return {
    ...actual,
    useQuery: mockUseQuery,
  };
});

// Mock server actions
vi.mock('@/server/actions/organization', () => ({
  setOrganizationId: vi.fn(),
  clearOrganizationId: vi.fn(),
}));

// Create a mock store that actually manages state
const createMockStore = () => {
  const state = {
    currentOrganization: null as Organization | null,
    organizations: [] as Organization[],
    isLoading: false,
    error: null as string | null,
  };

  const store = {
    ...state,
    setCurrentOrganization: vi.fn((org: Organization | null) => {
      state.currentOrganization = org;
      Object.assign(store, state);
    }),
    setOrganizations: vi.fn((orgs: Organization[]) => {
      state.organizations = orgs;
      Object.assign(store, state);
    }),
    setLoading: vi.fn((loading: boolean) => {
      state.isLoading = loading;
      Object.assign(store, state);
    }),
    setError: vi.fn((error: string | null) => {
      state.error = error;
      Object.assign(store, state);
    }),
    resetOrganizationState: vi.fn(() => {
      state.currentOrganization = null;
      state.organizations = [];
      state.isLoading = false;
      state.error = null;
      Object.assign(store, state);
    }),
  };

  return store;
};

let mockStore = createMockStore();

// Mock the store
vi.mock('@/stores', () => ({
  useOrganizationStore: vi.fn(() => mockStore),
}));

// Test wrapper component
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

  const TestWrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      <CohereClientProvider client={mockCohereClient as any}>
        {children}
      </CohereClientProvider>
    </QueryClientProvider>
  );

  return TestWrapper;
};

describe('useOrganization', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Reset the organization store state
    mockStore = createMockStore();
    vi.mocked(useOrganizationStore).mockReturnValue(mockStore);

    // Set up default useQuery mock behavior
    mockUseQuery.mockReturnValue({
      data: mockOrganizations,
      isLoading: false,
      error: null,
      refetch: vi.fn(),
    });
  });

  it('should fetch organizations on mount', async () => {
    // Mock useQuery to return organizations data
    mockUseQuery.mockReturnValue({
      data: mockOrganizations,
      isLoading: false,
      error: null,
      refetch: vi.fn(),
    });

    const { result } = renderHook(() => useOrganization(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.organizations).toEqual(mockOrganizations);
    });
  });

  it('should set first organization as current when none is selected', async () => {
    // Mock useQuery to return organizations data
    mockUseQuery.mockReturnValue({
      data: mockOrganizations,
      isLoading: false,
      error: null,
      refetch: vi.fn(),
    });

    const { result } = renderHook(() => useOrganization(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.currentOrganization).toEqual(mockOrganizations[0]);
    });
  });

  it('should update CohereClient organization ID when current organization changes', async () => {
    // Mock useQuery to return organizations data
    mockUseQuery.mockReturnValue({
      data: mockOrganizations,
      isLoading: false,
      error: null,
      refetch: vi.fn(),
    });

    const { result } = renderHook(() => useOrganization(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.currentOrganization).toEqual(mockOrganizations[0]);
    });

    await waitFor(() => {
      expect(mockCohereClient.setOrganizationId).toHaveBeenCalledWith(mockOrganizations[0].id);
    });
  });

  it('should switch organization correctly', async () => {
    // Mock useQuery to return organizations data
    mockUseQuery.mockReturnValue({
      data: mockOrganizations,
      isLoading: false,
      error: null,
      refetch: vi.fn(),
    });

    const { result } = renderHook(() => useOrganization(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.organizations).toEqual(mockOrganizations);
    });

    // Switch to second organization
    result.current.switchOrganization(mockOrganizations[1]);

    await waitFor(() => {
      expect(result.current.currentOrganization).toEqual(mockOrganizations[1]);
    });

    expect(mockCohereClient.setOrganizationId).toHaveBeenCalledWith(mockOrganizations[1].id);
  });

  it('should handle switching to null organization', async () => {
    // Mock useQuery to return organizations data
    mockUseQuery.mockReturnValue({
      data: mockOrganizations,
      isLoading: false,
      error: null,
      refetch: vi.fn(),
    });

    const { result } = renderHook(() => useOrganization(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.currentOrganization).toEqual(mockOrganizations[0]);
    });

    // Switch to null
    result.current.switchOrganization(null);

    await waitFor(() => {
      expect(result.current.currentOrganization).toBeNull();
    });

    expect(mockCohereClient.setOrganizationId).toHaveBeenCalledWith(undefined);
  });

  it('should find organization by ID', async () => {
    // Mock useQuery to return organizations data
    mockUseQuery.mockReturnValue({
      data: mockOrganizations,
      isLoading: false,
      error: null,
      refetch: vi.fn(),
    });

    const { result } = renderHook(() => useOrganization(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.organizations).toEqual(mockOrganizations);
    });

    const foundOrg = result.current.getOrganizationById('org-2');
    expect(foundOrg).toEqual(mockOrganizations[1]);

    const notFoundOrg = result.current.getOrganizationById('non-existent');
    expect(notFoundOrg).toBeUndefined();
  });

  it('should check access to organization', async () => {
    // Mock useQuery to return organizations data
    mockUseQuery.mockReturnValue({
      data: mockOrganizations,
      isLoading: false,
      error: null,
      refetch: vi.fn(),
    });

    const { result } = renderHook(() => useOrganization(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.organizations).toEqual(mockOrganizations);
    });

    expect(result.current.hasAccessToOrganization('org-1')).toBe(true);
    expect(result.current.hasAccessToOrganization('org-2')).toBe(true);
    expect(result.current.hasAccessToOrganization('non-existent')).toBe(false);
  });

  it('should handle API errors', async () => {
    const errorMessage = 'Failed to fetch organizations';
    // Mock useQuery to return error state
    mockUseQuery.mockReturnValue({
      data: undefined,
      isLoading: false,
      error: new Error(errorMessage),
      refetch: vi.fn(),
    });

    const { result } = renderHook(() => useOrganization(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.error).toBe(errorMessage);
    });
  });

  it('should provide computed values correctly', async () => {
    // Mock useQuery to return organizations data
    mockUseQuery.mockReturnValue({
      data: mockOrganizations,
      isLoading: false,
      error: null,
      refetch: vi.fn(),
    });

    const { result } = renderHook(() => useOrganization(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.hasOrganizations).toBe(true);
      expect(result.current.currentOrganizationId).toBe(mockOrganizations[0].id);
    });
  });
});

describe('useCurrentOrganization', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Reset the organization store state
    const store = useOrganizationStore.getState();
    store.resetOrganizationState();
  });

  it('should return current organization context', () => {
    const { result } = renderHook(() => useCurrentOrganization());

    expect(result.current.currentOrganization).toBeNull();
    expect(result.current.currentOrganizationId).toBeUndefined();
  });

  it('should update when organization changes', () => {
    const { result } = renderHook(() => useCurrentOrganization());

    // Initially null
    expect(result.current.currentOrganization).toBeNull();

    // Set organization through store
    const store = useOrganizationStore.getState();
    store.setCurrentOrganization(mockOrganizations[0]);

    // Should reflect the change
    expect(result.current.currentOrganization).toEqual(mockOrganizations[0]);
    expect(result.current.currentOrganizationId).toBe(mockOrganizations[0].id);
  });
});
