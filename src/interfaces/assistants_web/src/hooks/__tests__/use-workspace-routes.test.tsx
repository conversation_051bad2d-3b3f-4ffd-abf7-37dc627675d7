import { usePara<PERSON>, useRouter } from 'next/navigation';
import { renderHook } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';

import { useWorkspaceRoutes, useWorkspaceUrls } from '../use-workspace-routes';
import { useCurrentOrganization } from '../use-organization';

// Mock Next.js navigation hooks
vi.mock('next/navigation', () => ({
  useParams: vi.fn(),
  useRouter: vi.fn(),
}));

// Mock the organization hook
const mockUseCurrentOrganization = vi.fn(() => ({
  currentOrganizationId: 'org-123',
}));

vi.mock('../use-organization', () => ({
  useCurrentOrganization: mockUseCurrentOrganization,
}));

const mockPush = vi.fn();
const mockRouter = { push: mockPush };

describe('useWorkspaceRoutes', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    (useRouter as any).mockReturnValue(mockRouter);
    (useParams as any).mockReturnValue({ workspaceId: 'workspace-456' });
    
    // Mock window.location
    Object.defineProperty(window, 'location', {
      value: {
        pathname: '/workspace-456/c/conv-123',
      },
      writable: true,
    });
  });

  it('should use workspace ID from params when available', () => {
    const { result } = renderHook(() => useWorkspaceRoutes());
    
    expect(result.current.workspaceId).toBe('workspace-456');
    expect(result.current.isWorkspaceRoute).toBe(true);
  });

  it('should fall back to current organization ID when no workspace in params', () => {
    (useParams as any).mockReturnValue({});
    
    const { result } = renderHook(() => useWorkspaceRoutes());
    
    expect(result.current.workspaceId).toBe('org-123');
    expect(result.current.isWorkspaceRoute).toBe(false);
  });

  it('should navigate to conversation correctly', () => {
    const { result } = renderHook(() => useWorkspaceRoutes());
    
    result.current.navigateToConversation('conv-123');
    expect(mockPush).toHaveBeenCalledWith('/workspace-456/c/conv-123');
    
    result.current.navigateToConversation('conv-456', 'agent-789');
    expect(mockPush).toHaveBeenCalledWith('/workspace-456/a/agent-789/c/conv-456');
  });

  it('should navigate to new chat correctly', () => {
    const { result } = renderHook(() => useWorkspaceRoutes());
    
    result.current.navigateToNewChat();
    expect(mockPush).toHaveBeenCalledWith('/workspace-456');
    
    result.current.navigateToNewChat('agent-789');
    expect(mockPush).toHaveBeenCalledWith('/workspace-456/a/agent-789');
  });

  it('should navigate to edit agent correctly', () => {
    const { result } = renderHook(() => useWorkspaceRoutes());
    
    result.current.navigateToEditAgent('agent-123');
    expect(mockPush).toHaveBeenCalledWith('/workspace-456/edit/agent-123');
  });

  it('should navigate to create agent correctly', () => {
    const { result } = renderHook(() => useWorkspaceRoutes());
    
    result.current.navigateToCreateAgent();
    expect(mockPush).toHaveBeenCalledWith('/workspace-456/new');
  });

  it('should navigate to discover correctly', () => {
    const { result } = renderHook(() => useWorkspaceRoutes());
    
    result.current.navigateToDiscover();
    expect(mockPush).toHaveBeenCalledWith('/workspace-456/discover');
  });

  it('should navigate to settings correctly', () => {
    const { result } = renderHook(() => useWorkspaceRoutes());
    
    result.current.navigateToSettings();
    expect(mockPush).toHaveBeenCalledWith('/workspace-456/settings');
  });

  it('should switch workspace correctly', () => {
    const { result } = renderHook(() => useWorkspaceRoutes());
    
    // Switch without preserving path
    result.current.switchWorkspace('new-workspace');
    expect(mockPush).toHaveBeenCalledWith('/new-workspace');
    
    // Switch with preserving path
    result.current.switchWorkspace('new-workspace', true);
    expect(mockPush).toHaveBeenCalledWith('/new-workspace/c/conv-123');
  });

  it('should generate conversation URLs correctly', () => {
    const { result } = renderHook(() => useWorkspaceRoutes());
    
    expect(result.current.getConversationUrl('conv-123')).toBe('/workspace-456/c/conv-123');
    expect(result.current.getConversationUrl('conv-123', 'agent-789')).toBe('/workspace-456/a/agent-789/c/conv-123');
  });

  it('should check workspace membership correctly', () => {
    const { result } = renderHook(() => useWorkspaceRoutes());
    
    expect(result.current.isInWorkspace('workspace-456')).toBe(true);
    expect(result.current.isInWorkspace('other-workspace')).toBe(false);
  });

  it('should handle missing workspace ID gracefully', () => {
    (useParams as any).mockReturnValue({});
    mockUseCurrentOrganization.mockReturnValue({ currentOrganizationId: undefined });

    const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

    const { result } = renderHook(() => useWorkspaceRoutes());

    result.current.navigateToConversation('conv-123');
    expect(consoleSpy).toHaveBeenCalledWith('No workspace ID available for navigation');
    expect(mockPush).not.toHaveBeenCalled();

    consoleSpy.mockRestore();
  });
});

describe('useWorkspaceUrls', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    (useParams as any).mockReturnValue({ workspaceId: 'workspace-456' });
  });

  it('should generate URLs correctly', () => {
    const { result } = renderHook(() => useWorkspaceUrls());
    
    expect(result.current.getConversationUrl('conv-123')).toBe('/workspace-456/c/conv-123');
    expect(result.current.getConversationUrl('conv-123', 'agent-789')).toBe('/workspace-456/a/agent-789/c/conv-123');
    expect(result.current.getAgentUrl('agent-123')).toBe('/workspace-456/a/agent-123');
    expect(result.current.getEditAgentUrl('agent-123')).toBe('/workspace-456/edit/agent-123');
  });

  it('should return null when no workspace ID available', () => {
    (useParams as any).mockReturnValue({});
    mockUseCurrentOrganization.mockReturnValue({ currentOrganizationId: undefined });

    const { result } = renderHook(() => useWorkspaceUrls());

    expect(result.current.getConversationUrl('conv-123')).toBeNull();
    expect(result.current.getAgentUrl('agent-123')).toBeNull();
    expect(result.current.getEditAgentUrl('agent-123')).toBeNull();
  });
});
