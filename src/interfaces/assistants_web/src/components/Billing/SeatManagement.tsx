'use client';

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import React, { useState } from 'react';

import { Button, Input, Modal, Text } from '@/components/UI';
import { useCohereClient } from '@/cohere-client';
import { useCurrentOrganization } from '@/hooks/use-organization';

interface Props {
  isOpen: boolean;
  onClose: () => void;
}

export const SeatManagementModal: React.FC<Props> = ({ isOpen, onClose }) => {
  const [newSeatCount, setNewSeatCount] = useState<number>(0);
  const [error, setError] = useState<string | null>(null);

  const cohereClient = useCohereClient();
  const queryClient = useQueryClient();
  const { currentOrganization } = useCurrentOrganization();

  const {
    data: billingInfo,
    isLoading: isBillingLoading,
  } = useQuery({
    queryKey: ['billing-info', currentOrganization?.id],
    queryFn: () => cohereClient.getBillingInfo({ workspaceId: currentOrganization!.id }),
    enabled: !!currentOrganization && isOpen,
  });

  const {
    data: members = [],
  } = useQuery({
    queryKey: ['workspace-members', currentOrganization?.id],
    queryFn: () => cohereClient.getWorkspaceMembers({ workspaceId: currentOrganization!.id }),
    enabled: !!currentOrganization && isOpen,
  });

  const updateSeatsMutation = useMutation({
    mutationFn: async (seatCount: number) => {
      return cohereClient.updateSeatCount({
        workspaceId: currentOrganization!.id,
        seatCount,
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['billing-info'] });
      handleClose();
    },
    onError: (error: any) => {
      setError(error.message || 'Failed to update seat count');
    },
  });

  React.useEffect(() => {
    if (billingInfo && isOpen) {
      setNewSeatCount(billingInfo.seat_count);
    }
  }, [billingInfo, isOpen]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    if (!billingInfo) return;

    if (newSeatCount < members.length) {
      setError(`Cannot reduce seats below current member count (${members.length})`);
      return;
    }

    if (newSeatCount === billingInfo.seat_count) {
      handleClose();
      return;
    }

    updateSeatsMutation.mutate(newSeatCount);
  };

  const handleClose = () => {
    setError(null);
    setNewSeatCount(0);
    onClose();
  };

  if (!billingInfo || isBillingLoading) {
    return (
      <Modal isOpen={isOpen} onClose={handleClose} title="Manage Seats">
        <div className="flex justify-center py-8">
          <Text>Loading billing information...</Text>
        </div>
      </Modal>
    );
  }

  const currentSeats = billingInfo.seat_count;
  const activeMembers = members.length;
  const seatDifference = newSeatCount - currentSeats;
  const costDifference = seatDifference * billingInfo.amount_per_seat;
  const isIncrease = seatDifference > 0;
  const isDecrease = seatDifference < 0;

  return (
    <Modal isOpen={isOpen} onClose={handleClose} title="Manage Seats">
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Current Status */}
        <div className="bg-gray-50 rounded-lg p-4">
          <Text className="font-medium mb-2">Current Status</Text>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <Text className="text-gray-600">Current Seats</Text>
              <Text className="font-medium">{currentSeats}</Text>
            </div>
            <div>
              <Text className="text-gray-600">Active Members</Text>
              <Text className="font-medium">{activeMembers}</Text>
            </div>
            <div>
              <Text className="text-gray-600">Available Seats</Text>
              <Text className="font-medium">{currentSeats - activeMembers}</Text>
            </div>
            <div>
              <Text className="text-gray-600">Price per Seat</Text>
              <Text className="font-medium">
                {billingInfo.currency.toUpperCase()} {billingInfo.amount_per_seat}
              </Text>
            </div>
          </div>
        </div>

        {/* Seat Count Input */}
        <div>
          <Text as="label" className="block text-sm font-medium mb-2">
            New Seat Count
          </Text>
          <Input
            type="text"
            value={newSeatCount.toString()}
            onChange={(e) => setNewSeatCount(parseInt(e.target.value) || 0)}
            className="w-full"
          />
          <Text className="text-gray-500 text-sm mt-1">
            Minimum: {activeMembers} (current active members)
          </Text>
        </div>

        {/* Cost Preview */}
        {seatDifference !== 0 && (
          <div className={`rounded-lg p-4 ${
            isIncrease ? 'bg-blue-50 border border-blue-200' : 'bg-green-50 border border-green-200'
          }`}>
            <Text className="font-medium mb-2">
              {isIncrease ? 'Additional Cost' : 'Cost Savings'}
            </Text>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <Text>Seat change:</Text>
                <Text className={isIncrease ? 'text-blue-600' : 'text-green-600'}>
                  {isIncrease ? '+' : ''}{seatDifference} seats
                </Text>
              </div>
              <div className="flex justify-between">
                <Text>
                  {billingInfo.billing_cycle === 'yearly' ? 'Annual' : 'Monthly'} change:
                </Text>
                <Text className={`font-medium ${isIncrease ? 'text-blue-600' : 'text-green-600'}`}>
                  {isIncrease ? '+' : ''}{billingInfo.currency.toUpperCase()} {Math.abs(costDifference)}
                </Text>
              </div>
              <div className="flex justify-between font-medium">
                <Text>New total:</Text>
                <Text>
                  {billingInfo.currency.toUpperCase()} {newSeatCount * billingInfo.amount_per_seat}
                </Text>
              </div>
            </div>
            
            {isIncrease && (
              <Text className="text-blue-700 text-xs mt-2">
                Changes will be prorated and reflected in your next billing cycle.
              </Text>
            )}
            
            {isDecrease && (
              <Text className="text-green-700 text-xs mt-2">
                Seat reduction will take effect at the end of your current billing period.
              </Text>
            )}
          </div>
        )}

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-3">
            <Text className="text-red-800 text-sm">{error}</Text>
          </div>
        )}

        {/* Actions */}
        <div className="flex justify-end space-x-3 pt-4">
          <Button
            buttonType="button"
            kind="secondary"
            onClick={handleClose}
            disabled={updateSeatsMutation.isPending}
          >
            Cancel
          </Button>
          <Button
            buttonType="submit"
            isLoading={updateSeatsMutation.isPending}
            disabled={newSeatCount === currentSeats || newSeatCount < activeMembers}
          >
            {isIncrease ? 'Add Seats' : isDecrease ? 'Reduce Seats' : 'Update Seats'}
          </Button>
        </div>
      </form>
    </Modal>
  );
};
