'use client';

import { useQuery } from '@tanstack/react-query';
import React from 'react';

import { <PERSON><PERSON>, <PERSON><PERSON>, Text } from '@/components/UI';
import { useCohereClient } from '@/cohere-client';
import { useCurrentOrganization } from '@/hooks/use-organization';
import { BillingStatus } from '@/types/invitation';

export const BillingOverview: React.FC = () => {
  const cohereClient = useCohereClient();
  const { currentOrganization } = useCurrentOrganization();

  const {
    data: billingInfo,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['billing-info', currentOrganization?.id],
    queryFn: () => cohereClient.getBillingInfo({ workspaceId: currentOrganization!.id }),
    enabled: !!currentOrganization,
  });

  const {
    data: members = [],
  } = useQuery({
    queryKey: ['workspace-members', currentOrganization?.id],
    queryFn: () => cohereClient.getWorkspaceMembers({ workspaceId: currentOrganization!.id }),
    enabled: !!currentOrganization,
  });

  if (!currentOrganization) {
    return (
      <div className="text-center py-8">
        <Text className="text-gray-500">No workspace selected</Text>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex justify-center py-8">
        <Spinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <Text className="text-red-500">Failed to load billing information</Text>
      </div>
    );
  }

  if (!billingInfo) {
    return (
      <div className="text-center py-8">
        <Text className="text-gray-500 mb-4">No billing information available</Text>
        <Button>Set up billing</Button>
      </div>
    );
  }

  const getStatusColor = (status: BillingStatus) => {
    switch (status) {
      case BillingStatus.ACTIVE:
        return 'text-green-600 bg-green-100';
      case BillingStatus.TRIALING:
        return 'text-blue-600 bg-blue-100';
      case BillingStatus.PAST_DUE:
      case BillingStatus.UNPAID:
        return 'text-red-600 bg-red-100';
      case BillingStatus.CANCELED:
        return 'text-gray-600 bg-gray-100';
      default:
        return 'text-yellow-600 bg-yellow-100';
    }
  };

  const getStatusLabel = (status: BillingStatus) => {
    switch (status) {
      case BillingStatus.ACTIVE:
        return 'Active';
      case BillingStatus.TRIALING:
        return 'Trial';
      case BillingStatus.PAST_DUE:
        return 'Past Due';
      case BillingStatus.CANCELED:
        return 'Canceled';
      case BillingStatus.INCOMPLETE:
        return 'Incomplete';
      case BillingStatus.INCOMPLETE_EXPIRED:
        return 'Incomplete (Expired)';
      case BillingStatus.UNPAID:
        return 'Unpaid';
      default:
        return status;
    }
  };

  const monthlyTotal = billingInfo.seat_count * billingInfo.amount_per_seat;
  const yearlyTotal = billingInfo.billing_cycle === 'yearly' ? monthlyTotal * 12 : monthlyTotal;

  return (
    <div className="space-y-6">
      <div>
        <Text as="h2" className="text-xl font-semibold">
          Billing Overview
        </Text>
        <Text className="text-gray-600 mt-1">
          Manage your workspace billing and subscription
        </Text>
      </div>

      {/* Current Plan */}
      <div className="bg-white rounded-lg border p-6">
        <div className="flex justify-between items-start mb-4">
          <div>
            <Text className="text-lg font-semibold capitalize">
              {billingInfo.plan_type} Plan
            </Text>
            <div className="flex items-center mt-1">
              <span
                className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(
                  billingInfo.status
                )}`}
              >
                {getStatusLabel(billingInfo.status)}
              </span>
            </div>
          </div>
          <Button kind="secondary">Manage Subscription</Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-gray-50 rounded-lg p-4">
            <Text className="text-sm font-medium text-gray-700">Current Seats</Text>
            <Text className="text-2xl font-bold text-gray-900 mt-1">
              {billingInfo.seat_count}
            </Text>
            <Text className="text-sm text-gray-600">
              {members.length} active members
            </Text>
          </div>

          <div className="bg-gray-50 rounded-lg p-4">
            <Text className="text-sm font-medium text-gray-700">
              Price per Seat
            </Text>
            <Text className="text-2xl font-bold text-gray-900 mt-1">
              {billingInfo.currency.toUpperCase()} {billingInfo.amount_per_seat}
            </Text>
            <Text className="text-sm text-gray-600 capitalize">
              per {billingInfo.billing_cycle === 'yearly' ? 'year' : 'month'}
            </Text>
          </div>

          <div className="bg-gray-50 rounded-lg p-4">
            <Text className="text-sm font-medium text-gray-700">
              {billingInfo.billing_cycle === 'yearly' ? 'Annual' : 'Monthly'} Total
            </Text>
            <Text className="text-2xl font-bold text-gray-900 mt-1">
              {billingInfo.currency.toUpperCase()} {
                billingInfo.billing_cycle === 'yearly' ? yearlyTotal : monthlyTotal
              }
            </Text>
            {billingInfo.next_billing_date && (
              <Text className="text-sm text-gray-600">
                Next billing: {new Date(billingInfo.next_billing_date).toLocaleDateString()}
              </Text>
            )}
          </div>
        </div>
      </div>

      {/* Usage Details */}
      <div className="bg-white rounded-lg border p-6">
        <Text className="text-lg font-semibold mb-4">Usage Details</Text>
        
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <Text className="font-medium">Seats Purchased</Text>
            <Text>{billingInfo.seat_count}</Text>
          </div>
          
          <div className="flex justify-between items-center">
            <Text className="font-medium">Active Members</Text>
            <Text>{members.length}</Text>
          </div>
          
          <div className="flex justify-between items-center">
            <Text className="font-medium">Available Seats</Text>
            <Text className="text-green-600">
              {billingInfo.seat_count - members.length}
            </Text>
          </div>
          
          {members.length >= billingInfo.seat_count && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <Text className="text-yellow-800 font-medium">
                Seat Limit Reached
              </Text>
              <Text className="text-yellow-700 text-sm mt-1">
                You&apos;ve reached your seat limit. Add more seats to invite additional members.
              </Text>
              <Button className="mt-2">
                Add More Seats
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Billing Actions */}
      <div className="bg-white rounded-lg border p-6">
        <Text className="text-lg font-semibold mb-4">Billing Actions</Text>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Button kind="secondary" className="justify-start">
            Update Payment Method
          </Button>
          
          <Button kind="secondary" className="justify-start">
            Download Invoices
          </Button>

          <Button kind="secondary" className="justify-start">
            Change Billing Cycle
          </Button>

          <Button kind="secondary" className="justify-start text-red-600">
            Cancel Subscription
          </Button>
        </div>
      </div>
    </div>
  );
};
