import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { render, screen, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';

import { CohereClientProvider, Organization } from '@/cohere-client';
import { BillingInfo, BillingStatus, BillingPlan, BillingCycle, WorkspaceMember, WorkspaceRole } from '@/types/invitation';

import { BillingOverview } from '../BillingOverview';

// Mock the CohereClient
const mockCohereClient = {
  getBillingInfo: vi.fn(),
  getWorkspaceMembers: vi.fn(),
};

// Mock organization data
const mockOrganization: Organization = {
  id: 'workspace-123',
  name: 'Test Workspace',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
};

// Mock billing data
const mockBillingInfo: BillingInfo = {
  workspace_id: 'workspace-123',
  stripe_customer_id: 'cus_123',
  stripe_subscription_id: 'sub_123',
  plan_type: BillingPlan.PROFESSIONAL,
  seat_count: 10,
  billing_cycle: BillingCycle.MONTHLY,
  amount_per_seat: 25,
  currency: 'usd',
  next_billing_date: '2024-02-01T00:00:00Z',
  status: BillingStatus.ACTIVE,
};

// Mock members data
const mockMembers: WorkspaceMember[] = [
  {
    id: 'member-1',
    user_id: 'user-1',
    workspace_id: 'workspace-123',
    email: '<EMAIL>',
    name: 'User One',
    role: WorkspaceRole.ADMIN,
    joined_at: '2024-01-01T00:00:00Z',
  },
  {
    id: 'member-2',
    user_id: 'user-2',
    workspace_id: 'workspace-123',
    email: '<EMAIL>',
    name: 'User Two',
    role: WorkspaceRole.MEMBER,
    joined_at: '2024-01-02T00:00:00Z',
  },
];

// Mock the useCohereClient hook
vi.mock('@/cohere-client', async () => {
  const actual = await vi.importActual('@/cohere-client');
  return {
    ...actual,
    useCohereClient: () => mockCohereClient,
  };
});

// Mock the organization hook
vi.mock('@/hooks/use-organization', () => ({
  useCurrentOrganization: () => ({
    currentOrganization: mockOrganization,
  }),
}));

// Test wrapper
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

  const TestWrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      <CohereClientProvider client={mockCohereClient as any}>
        {children}
      </CohereClientProvider>
    </QueryClientProvider>
  );

  return TestWrapper;
};

describe('BillingOverview', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockCohereClient.getBillingInfo.mockResolvedValue(mockBillingInfo);
    mockCohereClient.getWorkspaceMembers.mockResolvedValue(mockMembers);
  });

  it('should render billing information correctly', async () => {
    const Wrapper = createWrapper();
    
    render(
      <Wrapper>
        <BillingOverview />
      </Wrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Billing Overview')).toBeInTheDocument();
    });

    expect(screen.getByText('Professional Plan')).toBeInTheDocument();
    expect(screen.getByText('Active')).toBeInTheDocument();
    expect(screen.getByText('10')).toBeInTheDocument(); // seat count
    expect(screen.getByText('2 active members')).toBeInTheDocument();
    expect(screen.getByText('USD 25')).toBeInTheDocument(); // price per seat
    expect(screen.getByText('USD 250')).toBeInTheDocument(); // monthly total
  });

  it('should show seat limit warning when at capacity', async () => {
    // Mock billing info with same seat count as members
    const fullBillingInfo = {
      ...mockBillingInfo,
      seat_count: 2, // Same as number of members
    };
    mockCohereClient.getBillingInfo.mockResolvedValue(fullBillingInfo);

    const Wrapper = createWrapper();
    
    render(
      <Wrapper>
        <BillingOverview />
      </Wrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Seat Limit Reached')).toBeInTheDocument();
    });

    expect(screen.getByText("You've reached your seat limit. Add more seats to invite additional members.")).toBeInTheDocument();
    expect(screen.getByText('Add More Seats')).toBeInTheDocument();
  });

  it('should display different status colors correctly', async () => {
    const trialBillingInfo = {
      ...mockBillingInfo,
      status: BillingStatus.TRIALING,
    };
    mockCohereClient.getBillingInfo.mockResolvedValue(trialBillingInfo);

    const Wrapper = createWrapper();
    
    render(
      <Wrapper>
        <BillingOverview />
      </Wrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Trial')).toBeInTheDocument();
    });
  });

  it('should handle yearly billing cycle', async () => {
    const yearlyBillingInfo = {
      ...mockBillingInfo,
      billing_cycle: BillingCycle.YEARLY,
      amount_per_seat: 250, // Yearly price
    };
    mockCohereClient.getBillingInfo.mockResolvedValue(yearlyBillingInfo);

    const Wrapper = createWrapper();
    
    render(
      <Wrapper>
        <BillingOverview />
      </Wrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('per year')).toBeInTheDocument();
    });

    expect(screen.getByText('Annual Total')).toBeInTheDocument();
    expect(screen.getByText('USD 2500')).toBeInTheDocument(); // 10 seats * 250
  });

  it('should handle no billing info', async () => {
    mockCohereClient.getBillingInfo.mockResolvedValue(null);

    const Wrapper = createWrapper();
    
    render(
      <Wrapper>
        <BillingOverview />
      </Wrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('No billing information available')).toBeInTheDocument();
    });

    expect(screen.getByText('Set up billing')).toBeInTheDocument();
  });

  it('should handle API errors', async () => {
    mockCohereClient.getBillingInfo.mockRejectedValue(new Error('API Error'));

    const Wrapper = createWrapper();
    
    render(
      <Wrapper>
        <BillingOverview />
      </Wrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Failed to load billing information')).toBeInTheDocument();
    });
  });

  it('should show loading state', () => {
    mockCohereClient.getBillingInfo.mockImplementation(() => new Promise(() => {})); // Never resolves

    const Wrapper = createWrapper();
    
    render(
      <Wrapper>
        <BillingOverview />
      </Wrapper>
    );

    expect(screen.getByText('Billing Overview')).toBeInTheDocument();
    // Should show spinner while loading
  });

  it('should display billing actions', async () => {
    const Wrapper = createWrapper();
    
    render(
      <Wrapper>
        <BillingOverview />
      </Wrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Billing Actions')).toBeInTheDocument();
    });

    expect(screen.getByText('Update Payment Method')).toBeInTheDocument();
    expect(screen.getByText('Download Invoices')).toBeInTheDocument();
    expect(screen.getByText('Change Billing Cycle')).toBeInTheDocument();
    expect(screen.getByText('Cancel Subscription')).toBeInTheDocument();
  });
});
