import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';

import { CohereClientProvider, Organization } from '@/cohere-client';
import { BillingInfo, BillingStatus, BillingPlan, BillingCycle, WorkspaceMember, WorkspaceRole } from '@/types/invitation';

import { SeatManagementModal } from '../SeatManagement';

// Mock the CohereClient
const mockCohereClient = {
  getBillingInfo: vi.fn(),
  getWorkspaceMembers: vi.fn(),
  updateSeatCount: vi.fn(),
};

// Mock organization data
const mockOrganization: Organization = {
  id: 'workspace-123',
  name: 'Test Workspace',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
};

// Mock billing data
const mockBillingInfo: BillingInfo = {
  workspace_id: 'workspace-123',
  stripe_customer_id: 'cus_123',
  stripe_subscription_id: 'sub_123',
  plan_type: BillingPlan.PROFESSIONAL,
  seat_count: 10,
  billing_cycle: BillingCycle.MONTHLY,
  amount_per_seat: 25,
  currency: 'usd',
  next_billing_date: '2024-02-01T00:00:00Z',
  status: BillingStatus.ACTIVE,
};

// Mock members data
const mockMembers: WorkspaceMember[] = [
  {
    id: 'member-1',
    user_id: 'user-1',
    workspace_id: 'workspace-123',
    email: '<EMAIL>',
    name: 'User One',
    role: WorkspaceRole.ADMIN,
    joined_at: '2024-01-01T00:00:00Z',
  },
  {
    id: 'member-2',
    user_id: 'user-2',
    workspace_id: 'workspace-123',
    email: '<EMAIL>',
    name: 'User Two',
    role: WorkspaceRole.MEMBER,
    joined_at: '2024-01-02T00:00:00Z',
  },
];

// Mock the useCohereClient hook
vi.mock('@/cohere-client', async () => {
  const actual = await vi.importActual('@/cohere-client');
  return {
    ...actual,
    useCohereClient: () => mockCohereClient,
  };
});

// Mock the organization hook
vi.mock('@/hooks/use-organization', () => ({
  useCurrentOrganization: () => ({
    currentOrganization: mockOrganization,
  }),
}));

// Test wrapper
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

  const TestWrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      <CohereClientProvider client={mockCohereClient as any}>
        {children}
      </CohereClientProvider>
    </QueryClientProvider>
  );

  return TestWrapper;
};

describe('SeatManagementModal', () => {
  const mockOnClose = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    mockCohereClient.getBillingInfo.mockResolvedValue(mockBillingInfo);
    mockCohereClient.getWorkspaceMembers.mockResolvedValue(mockMembers);
  });

  it('should render modal when open', async () => {
    const Wrapper = createWrapper();
    
    render(
      <Wrapper>
        <SeatManagementModal isOpen={true} onClose={mockOnClose} />
      </Wrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Manage Seats')).toBeInTheDocument();
    });

    expect(screen.getByText('Current Status')).toBeInTheDocument();
    expect(screen.getByText('10')).toBeInTheDocument(); // Current seats
    expect(screen.getByText('2')).toBeInTheDocument(); // Active members
  });

  it('should not render modal when closed', () => {
    const Wrapper = createWrapper();
    
    render(
      <Wrapper>
        <SeatManagementModal isOpen={false} onClose={mockOnClose} />
      </Wrapper>
    );

    expect(screen.queryByText('Manage Seats')).not.toBeInTheDocument();
  });

  it('should show cost preview when increasing seats', async () => {
    const Wrapper = createWrapper();
    
    render(
      <Wrapper>
        <SeatManagementModal isOpen={true} onClose={mockOnClose} />
      </Wrapper>
    );

    await waitFor(() => {
      expect(screen.getByDisplayValue('10')).toBeInTheDocument();
    });

    // Increase seat count
    const seatInput = screen.getByDisplayValue('10');
    fireEvent.change(seatInput, { target: { value: '15' } });

    await waitFor(() => {
      expect(screen.getByText('Additional Cost')).toBeInTheDocument();
    });

    expect(screen.getByText('+5 seats')).toBeInTheDocument();
    expect(screen.getByText('+USD 125')).toBeInTheDocument(); // 5 * 25
    expect(screen.getByText('USD 375')).toBeInTheDocument(); // 15 * 25
  });

  it('should show cost savings when decreasing seats', async () => {
    const Wrapper = createWrapper();
    
    render(
      <Wrapper>
        <SeatManagementModal isOpen={true} onClose={mockOnClose} />
      </Wrapper>
    );

    await waitFor(() => {
      expect(screen.getByDisplayValue('10')).toBeInTheDocument();
    });

    // Decrease seat count
    const seatInput = screen.getByDisplayValue('10');
    fireEvent.change(seatInput, { target: { value: '8' } });

    await waitFor(() => {
      expect(screen.getByText('Cost Savings')).toBeInTheDocument();
    });

    expect(screen.getByText('-2 seats')).toBeInTheDocument();
    expect(screen.getByText('USD 50')).toBeInTheDocument(); // 2 * 25 (absolute value)
    expect(screen.getByText('USD 200')).toBeInTheDocument(); // 8 * 25
  });

  it('should prevent reducing seats below member count', async () => {
    const Wrapper = createWrapper();
    
    render(
      <Wrapper>
        <SeatManagementModal isOpen={true} onClose={mockOnClose} />
      </Wrapper>
    );

    await waitFor(() => {
      expect(screen.getByDisplayValue('10')).toBeInTheDocument();
    });

    // Try to set seats below member count
    const seatInput = screen.getByDisplayValue('10');
    fireEvent.change(seatInput, { target: { value: '1' } });

    const submitButton = screen.getByText('Update Seats');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Cannot reduce seats below current member count (2)')).toBeInTheDocument();
    });

    expect(mockCohereClient.updateSeatCount).not.toHaveBeenCalled();
  });

  it('should submit seat count update', async () => {
    mockCohereClient.updateSeatCount.mockResolvedValue({
      ...mockBillingInfo,
      seat_count: 15,
    });

    const Wrapper = createWrapper();
    
    render(
      <Wrapper>
        <SeatManagementModal isOpen={true} onClose={mockOnClose} />
      </Wrapper>
    );

    await waitFor(() => {
      expect(screen.getByDisplayValue('10')).toBeInTheDocument();
    });

    // Change seat count
    const seatInput = screen.getByDisplayValue('10');
    fireEvent.change(seatInput, { target: { value: '15' } });

    // Submit form
    const submitButton = screen.getByText('Add Seats');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockCohereClient.updateSeatCount).toHaveBeenCalledWith({
        workspaceId: 'workspace-123',
        seatCount: 15,
      });
    });

    await waitFor(() => {
      expect(mockOnClose).toHaveBeenCalled();
    });
  });

  it('should handle API errors', async () => {
    mockCohereClient.updateSeatCount.mockRejectedValue(new Error('Payment failed'));

    const Wrapper = createWrapper();
    
    render(
      <Wrapper>
        <SeatManagementModal isOpen={true} onClose={mockOnClose} />
      </Wrapper>
    );

    await waitFor(() => {
      expect(screen.getByDisplayValue('10')).toBeInTheDocument();
    });

    // Change seat count and submit
    const seatInput = screen.getByDisplayValue('10');
    fireEvent.change(seatInput, { target: { value: '15' } });

    const submitButton = screen.getByText('Add Seats');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Payment failed')).toBeInTheDocument();
    });

    expect(mockOnClose).not.toHaveBeenCalled();
  });

  it('should disable submit button when no changes', async () => {
    const Wrapper = createWrapper();
    
    render(
      <Wrapper>
        <SeatManagementModal isOpen={true} onClose={mockOnClose} />
      </Wrapper>
    );

    await waitFor(() => {
      const submitButton = screen.getByText('Update Seats');
      expect(submitButton).toBeDisabled();
    });
  });

  it('should show yearly billing information', async () => {
    const yearlyBillingInfo = {
      ...mockBillingInfo,
      billing_cycle: BillingCycle.YEARLY,
      amount_per_seat: 250,
    };
    mockCohereClient.getBillingInfo.mockResolvedValue(yearlyBillingInfo);

    const Wrapper = createWrapper();
    
    render(
      <Wrapper>
        <SeatManagementModal isOpen={true} onClose={mockOnClose} />
      </Wrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('USD 250')).toBeInTheDocument(); // Price per seat
    });

    // Change seat count to see annual calculation
    const seatInput = screen.getByDisplayValue('10');
    fireEvent.change(seatInput, { target: { value: '15' } });

    await waitFor(() => {
      expect(screen.getByText('Annual change:')).toBeInTheDocument();
    });
  });
});
