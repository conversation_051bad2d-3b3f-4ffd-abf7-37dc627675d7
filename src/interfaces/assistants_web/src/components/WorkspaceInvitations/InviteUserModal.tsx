'use client';

import { useMutation, useQueryClient } from '@tanstack/react-query';
import React, { useState } from 'react';

import { Button, Input, Modal, Text, Textarea } from '@/components/UI';
import { useCohereClient } from '@/cohere-client';
import { useCurrentOrganization } from '@/hooks/use-organization';
import { CreateInvitationRequest, WorkspaceRole } from '@/types/invitation';

interface Props {
  isOpen: boolean;
  onClose: () => void;
}

const ROLE_OPTIONS = [
  { value: WorkspaceRole.MEMBER, label: 'Member', description: 'Can create and manage conversations' },
  { value: WorkspaceRole.VIEWER, label: 'Viewer', description: 'Can view conversations but not create new ones' },
  { value: WorkspaceRole.ADMIN, label: 'Admin', description: 'Full access including user management' },
];

export const InviteUserModal: React.FC<Props> = ({ isOpen, onClose }) => {
  const [email, setEmail] = useState('');
  const [role, setRole] = useState<WorkspaceRole>(WorkspaceRole.MEMBER);
  const [message, setMessage] = useState('');
  const [errors, setErrors] = useState<Record<string, string>>({});

  const cohereClient = useCohereClient();
  const queryClient = useQueryClient();
  const { currentOrganization } = useCurrentOrganization();

  const inviteUserMutation = useMutation({
    mutationFn: async (data: CreateInvitationRequest) => {
      return cohereClient.createWorkspaceInvitation(data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['workspace-invitations'] });
      queryClient.invalidateQueries({ queryKey: ['workspace-members'] });
      handleClose();
    },
    onError: (error: any) => {
      setErrors({ submit: error.message || 'Failed to send invitation' });
    },
  });

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (!role) {
      newErrors.role = 'Role is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!currentOrganization) {
      setErrors({ submit: 'No workspace selected' });
      return;
    }

    if (!validateForm()) return;

    inviteUserMutation.mutate({
      workspace_id: currentOrganization.id,
      invitee_email: email.trim(),
      role,
      message: message.trim() || undefined,
    });
  };

  const handleClose = () => {
    setEmail('');
    setRole(WorkspaceRole.MEMBER);
    setMessage('');
    setErrors({});
    onClose();
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose} title="Invite User to Workspace">
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <Text as="label" className="block text-sm font-medium mb-1">
            Email Address
          </Text>
          <Input
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="<EMAIL>"
            className={errors.email ? 'border-red-500' : ''}
          />
          {errors.email && (
            <Text className="text-red-500 text-sm mt-1">{errors.email}</Text>
          )}
        </div>

        <div>
          <Text as="label" className="block text-sm font-medium mb-1">
            Role
          </Text>
          <select
            value={role}
            onChange={(e) => setRole(e.target.value as WorkspaceRole)}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.role ? 'border-red-500' : 'border-gray-300'
            }`}
          >
            {ROLE_OPTIONS.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label} - {option.description}
              </option>
            ))}
          </select>
          {errors.role && (
            <Text className="text-red-500 text-sm mt-1">{errors.role}</Text>
          )}
        </div>

        <div>
          <Text as="label" className="block text-sm font-medium mb-1">
            Personal Message (Optional)
          </Text>
          <Textarea
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            placeholder="Add a personal message to the invitation..."
            rows={3}
            maxLength={500}
          />
          <Text className="text-gray-500 text-xs mt-1">
            {message.length}/500 characters
          </Text>
        </div>

        {errors.submit && (
          <Text className="text-red-500 text-sm">{errors.submit}</Text>
        )}

        <div className="flex justify-end space-x-3 pt-4">
          <Button
            buttonType="button"
            kind="secondary"
            onClick={handleClose}
            disabled={inviteUserMutation.isPending}
          >
            Cancel
          </Button>
          <Button
            buttonType="submit"
            isLoading={inviteUserMutation.isPending}
            disabled={!email.trim() || !role}
          >
            Send Invitation
          </Button>
        </div>
      </form>
    </Modal>
  );
};
