'use client';

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import React, { useState } from 'react';

import { <PERSON><PERSON>, <PERSON><PERSON>, Text } from '@/components/UI';
import { useCohereClient } from '@/cohere-client';
import { useCurrentOrganization } from '@/hooks/use-organization';
import { WorkspaceMember, WorkspaceRole } from '@/types/invitation';

import { InviteUserModal } from './InviteUserModal';

const ROLE_LABELS = {
  [WorkspaceRole.ADMIN]: 'Admin',
  [WorkspaceRole.MEMBER]: 'Member',
  [WorkspaceRole.VIEWER]: 'Viewer',
};

export const WorkspaceMembers: React.FC = () => {
  const [isInviteModalOpen, setIsInviteModalOpen] = useState(false);
  const [memberToRemove, setMemberToRemove] = useState<WorkspaceMember | null>(null);

  const cohereClient = useCohereClient();
  const queryClient = useQueryClient();
  const { currentOrganization } = useCurrentOrganization();

  const {
    data: members = [],
    isLoading,
    error,
  } = useQuery({
    queryKey: ['workspace-members', currentOrganization?.id],
    queryFn: () => cohereClient.getWorkspaceMembers({ workspaceId: currentOrganization!.id }),
    enabled: !!currentOrganization,
  });

  const removeMemberMutation = useMutation({
    mutationFn: async (memberId: string) => {
      return cohereClient.removeWorkspaceMember({
        workspaceId: currentOrganization!.id,
        memberId,
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['workspace-members'] });
      setMemberToRemove(null);
    },
  });

  const updateMemberRoleMutation = useMutation({
    mutationFn: async ({ memberId, role }: { memberId: string; role: WorkspaceRole }) => {
      return cohereClient.updateWorkspaceMemberRole({
        workspaceId: currentOrganization!.id,
        memberId,
        role,
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['workspace-members'] });
    },
  });

  const handleRoleChange = (memberId: string, newRole: WorkspaceRole) => {
    updateMemberRoleMutation.mutate({ memberId, role: newRole });
  };

  const handleRemoveMember = (member: WorkspaceMember) => {
    if (window.confirm(`Are you sure you want to remove ${member.name || member.email} from the workspace?`)) {
      removeMemberMutation.mutate(member.id);
    }
  };

  if (!currentOrganization) {
    return (
      <div className="text-center py-8">
        <Text className="text-gray-500">No workspace selected</Text>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex justify-center py-8">
        <Spinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <Text className="text-red-500">Failed to load workspace members</Text>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <Text as="h2" className="text-xl font-semibold">
            Workspace Members
          </Text>
          <Text className="text-gray-600 mt-1">
            Manage who has access to {currentOrganization.name}
          </Text>
        </div>
        <Button onClick={() => setIsInviteModalOpen(true)}>
          Invite User
        </Button>
      </div>

      <div className="bg-white rounded-lg border">
        <div className="px-6 py-4 border-b">
          <Text className="font-medium">
            {members.length} {members.length === 1 ? 'member' : 'members'}
          </Text>
        </div>

        <div className="divide-y">
          {members.map((member) => (
            <div key={member.id} className="px-6 py-4 flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                  <Text className="text-blue-600 font-medium">
                    {(member.name || member.email).charAt(0).toUpperCase()}
                  </Text>
                </div>
                <div>
                  <Text className="font-medium">{member.name || 'Unknown'}</Text>
                  <Text className="text-gray-600 text-sm">{member.email}</Text>
                  {member.last_active_at && (
                    <Text className="text-gray-500 text-xs">
                      Last active: {new Date(member.last_active_at).toLocaleDateString()}
                    </Text>
                  )}
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <select
                  value={member.role}
                  onChange={(e) => handleRoleChange(member.id, e.target.value as WorkspaceRole)}
                  className="px-3 py-1 border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  disabled={updateMemberRoleMutation.isPending}
                >
                  {Object.entries(ROLE_LABELS).map(([value, label]) => (
                    <option key={value} value={value}>
                      {label}
                    </option>
                  ))}
                </select>

                <Button
                  kind="secondary"
                  onClick={() => handleRemoveMember(member)}
                  disabled={removeMemberMutation.isPending}
                  className="text-red-600 hover:text-red-700"
                >
                  Remove
                </Button>
              </div>
            </div>
          ))}
        </div>

        {members.length === 0 && (
          <div className="px-6 py-8 text-center">
            <Text className="text-gray-500">No members found</Text>
            <Button
              kind="secondary"
              className="mt-2"
              onClick={() => setIsInviteModalOpen(true)}
            >
              Invite your first member
            </Button>
          </div>
        )}
      </div>

      <InviteUserModal
        isOpen={isInviteModalOpen}
        onClose={() => setIsInviteModalOpen(false)}
      />
    </div>
  );
};
