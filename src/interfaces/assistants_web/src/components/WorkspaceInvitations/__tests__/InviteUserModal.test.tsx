import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';

import { CohereClientProvider, Organization } from '@/cohere-client';
import { WorkspaceRole } from '@/types/invitation';

import { InviteUserModal } from '../InviteUserModal';

// Mock the CohereClient
const mockCohereClient = {
  createWorkspaceInvitation: vi.fn(),
};

// Mock organization data
const mockOrganization: Organization = {
  id: 'workspace-123',
  name: 'Test Workspace',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
};

// Mock the useCohereClient hook
vi.mock('@/cohere-client', async () => {
  const actual = await vi.importActual('@/cohere-client');
  return {
    ...actual,
    useCohereClient: () => mockCohereClient,
  };
});

// Mock the organization hook
vi.mock('@/hooks/use-organization', () => ({
  useCurrentOrganization: () => ({
    currentOrganization: mockOrganization,
  }),
}));

// Test wrapper
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

  const TestWrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      <CohereClientProvider client={mockCohereClient as any}>
        {children}
      </CohereClientProvider>
    </QueryClientProvider>
  );

  return TestWrapper;
};

describe('InviteUserModal', () => {
  const mockOnClose = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render modal when open', () => {
    const Wrapper = createWrapper();
    
    render(
      <Wrapper>
        <InviteUserModal isOpen={true} onClose={mockOnClose} />
      </Wrapper>
    );

    expect(screen.getByText('Invite User to Workspace')).toBeInTheDocument();
    expect(screen.getByLabelText('Email Address')).toBeInTheDocument();
    expect(screen.getByLabelText('Role')).toBeInTheDocument();
    expect(screen.getByLabelText('Personal Message (Optional)')).toBeInTheDocument();
  });

  it('should not render modal when closed', () => {
    const Wrapper = createWrapper();
    
    render(
      <Wrapper>
        <InviteUserModal isOpen={false} onClose={mockOnClose} />
      </Wrapper>
    );

    expect(screen.queryByText('Invite User to Workspace')).not.toBeInTheDocument();
  });

  it('should validate email field', async () => {
    const Wrapper = createWrapper();
    
    render(
      <Wrapper>
        <InviteUserModal isOpen={true} onClose={mockOnClose} />
      </Wrapper>
    );

    const submitButton = screen.getByText('Send Invitation');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Email is required')).toBeInTheDocument();
    });

    // Test invalid email
    const emailInput = screen.getByLabelText('Email Address');
    fireEvent.change(emailInput, { target: { value: 'invalid-email' } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Please enter a valid email address')).toBeInTheDocument();
    });
  });

  it('should submit form with valid data', async () => {
    mockCohereClient.createWorkspaceInvitation.mockResolvedValue({
      id: 'invitation-123',
      status: 'pending',
    });

    const Wrapper = createWrapper();
    
    render(
      <Wrapper>
        <InviteUserModal isOpen={true} onClose={mockOnClose} />
      </Wrapper>
    );

    // Fill in form
    const emailInput = screen.getByLabelText('Email Address');
    const roleSelect = screen.getByLabelText('Role');
    const messageInput = screen.getByLabelText('Personal Message (Optional)');

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(roleSelect, { target: { value: WorkspaceRole.MEMBER } });
    fireEvent.change(messageInput, { target: { value: 'Welcome to our team!' } });

    // Submit form
    const submitButton = screen.getByText('Send Invitation');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockCohereClient.createWorkspaceInvitation).toHaveBeenCalledWith({
        workspace_id: 'workspace-123',
        invitee_email: '<EMAIL>',
        role: WorkspaceRole.MEMBER,
        message: 'Welcome to our team!',
      });
    });

    await waitFor(() => {
      expect(mockOnClose).toHaveBeenCalled();
    });
  });

  it('should handle API errors', async () => {
    mockCohereClient.createWorkspaceInvitation.mockRejectedValue(
      new Error('User already invited')
    );

    const Wrapper = createWrapper();
    
    render(
      <Wrapper>
        <InviteUserModal isOpen={true} onClose={mockOnClose} />
      </Wrapper>
    );

    // Fill in form
    const emailInput = screen.getByLabelText('Email Address');
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });

    // Submit form
    const submitButton = screen.getByText('Send Invitation');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('User already invited')).toBeInTheDocument();
    });

    expect(mockOnClose).not.toHaveBeenCalled();
  });

  it('should reset form when closed', () => {
    const Wrapper = createWrapper();
    
    const { rerender } = render(
      <Wrapper>
        <InviteUserModal isOpen={true} onClose={mockOnClose} />
      </Wrapper>
    );

    // Fill in form
    const emailInput = screen.getByLabelText('Email Address');
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });

    // Close modal
    const cancelButton = screen.getByText('Cancel');
    fireEvent.click(cancelButton);

    expect(mockOnClose).toHaveBeenCalled();

    // Reopen modal
    rerender(
      <Wrapper>
        <InviteUserModal isOpen={true} onClose={mockOnClose} />
      </Wrapper>
    );

    // Form should be reset
    const newEmailInput = screen.getByLabelText('Email Address');
    expect(newEmailInput).toHaveValue('');
  });

  it('should show character count for message', () => {
    const Wrapper = createWrapper();
    
    render(
      <Wrapper>
        <InviteUserModal isOpen={true} onClose={mockOnClose} />
      </Wrapper>
    );

    const messageInput = screen.getByLabelText('Personal Message (Optional)');
    fireEvent.change(messageInput, { target: { value: 'Hello world!' } });

    expect(screen.getByText('12/500 characters')).toBeInTheDocument();
  });

  it('should disable submit button when form is invalid', () => {
    const Wrapper = createWrapper();
    
    render(
      <Wrapper>
        <InviteUserModal isOpen={true} onClose={mockOnClose} />
      </Wrapper>
    );

    const submitButton = screen.getByText('Send Invitation');
    expect(submitButton).toBeDisabled();

    // Add email
    const emailInput = screen.getByLabelText('Email Address');
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });

    expect(submitButton).not.toBeDisabled();
  });
});
