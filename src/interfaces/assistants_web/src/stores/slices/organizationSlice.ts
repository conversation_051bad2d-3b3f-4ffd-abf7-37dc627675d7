import { StateCreator } from 'zustand';

import { Organization } from '@/cohere-client';

const INITIAL_STATE = {
  currentOrganization: null,
  organizations: [],
  isLoading: false,
  error: null,
};

type State = {
  currentOrganization: Organization | null;
  organizations: Organization[];
  isLoading: boolean;
  error: string | null;
};

type Actions = {
  setCurrentOrganization: (organization: Organization | null) => void;
  setOrganizations: (organizations: Organization[]) => void;
  setLoading: (isLoading: boolean) => void;
  setError: (error: string | null) => void;
  resetOrganizationState: () => void;
};

export type OrganizationStore = State & Actions;

export const createOrganizationSlice: StateCreator<
  OrganizationStore,
  [],
  [],
  OrganizationStore
> = (set) => ({
  ...INITIAL_STATE,
  setCurrentOrganization: (organization) =>
    set(() => ({
      currentOrganization: organization,
      error: null,
    })),
  setOrganizations: (organizations) =>
    set(() => ({
      organizations,
      error: null,
    })),
  setLoading: (isLoading) =>
    set(() => ({
      isLoading,
    })),
  setError: (error) =>
    set(() => ({
      error,
      isLoading: false,
    })),
  resetOrganizationState: () =>
    set(() => ({
      ...INITIAL_STATE,
    })),
});
