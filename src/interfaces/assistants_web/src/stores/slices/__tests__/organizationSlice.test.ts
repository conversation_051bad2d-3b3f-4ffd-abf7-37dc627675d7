import { describe, it, expect, beforeEach } from 'vitest';
import { create } from 'zustand';

import { Organization } from '@/cohere-client';
import { OrganizationStore, createOrganizationSlice } from '../organizationSlice';

// Mock organization data
const mockOrganization1: Organization = {
  id: 'org-1',
  name: 'Test Organization 1',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
};

const mockOrganization2: Organization = {
  id: 'org-2',
  name: 'Test Organization 2',
  created_at: '2024-01-02T00:00:00Z',
  updated_at: '2024-01-02T00:00:00Z',
};

const mockOrganizations = [mockOrganization1, mockOrganization2];

describe('organizationSlice', () => {
  let store: ReturnType<typeof create<OrganizationStore>>;

  beforeEach(() => {
    store = create<OrganizationStore>()(createOrganizationSlice);
  });

  describe('initial state', () => {
    it('should have correct initial state', () => {
      const state = store.getState();
      
      expect(state.currentOrganization).toBeNull();
      expect(state.organizations).toEqual([]);
      expect(state.isLoading).toBe(false);
      expect(state.error).toBeNull();
    });
  });

  describe('setCurrentOrganization', () => {
    it('should set current organization and clear error', () => {
      const { setCurrentOrganization } = store.getState();
      
      // First set an error
      store.getState().setError('Some error');
      expect(store.getState().error).toBe('Some error');
      
      // Set current organization
      setCurrentOrganization(mockOrganization1);
      
      const state = store.getState();
      expect(state.currentOrganization).toEqual(mockOrganization1);
      expect(state.error).toBeNull();
    });

    it('should allow setting current organization to null', () => {
      const { setCurrentOrganization } = store.getState();
      
      // First set an organization
      setCurrentOrganization(mockOrganization1);
      expect(store.getState().currentOrganization).toEqual(mockOrganization1);
      
      // Set to null
      setCurrentOrganization(null);
      expect(store.getState().currentOrganization).toBeNull();
    });
  });

  describe('setOrganizations', () => {
    it('should set organizations list and clear error', () => {
      const { setOrganizations } = store.getState();
      
      // First set an error
      store.getState().setError('Some error');
      expect(store.getState().error).toBe('Some error');
      
      // Set organizations
      setOrganizations(mockOrganizations);
      
      const state = store.getState();
      expect(state.organizations).toEqual(mockOrganizations);
      expect(state.error).toBeNull();
    });

    it('should allow setting empty organizations list', () => {
      const { setOrganizations } = store.getState();
      
      // First set some organizations
      setOrganizations(mockOrganizations);
      expect(store.getState().organizations).toEqual(mockOrganizations);
      
      // Set to empty array
      setOrganizations([]);
      expect(store.getState().organizations).toEqual([]);
    });
  });

  describe('setLoading', () => {
    it('should set loading state', () => {
      const { setLoading } = store.getState();
      
      expect(store.getState().isLoading).toBe(false);
      
      setLoading(true);
      expect(store.getState().isLoading).toBe(true);
      
      setLoading(false);
      expect(store.getState().isLoading).toBe(false);
    });
  });

  describe('setError', () => {
    it('should set error and stop loading', () => {
      const { setError, setLoading } = store.getState();
      
      // First set loading to true
      setLoading(true);
      expect(store.getState().isLoading).toBe(true);
      
      // Set error
      setError('Test error');
      
      const state = store.getState();
      expect(state.error).toBe('Test error');
      expect(state.isLoading).toBe(false);
    });

    it('should allow clearing error', () => {
      const { setError } = store.getState();
      
      // Set error
      setError('Test error');
      expect(store.getState().error).toBe('Test error');
      
      // Clear error
      setError(null);
      expect(store.getState().error).toBeNull();
    });
  });

  describe('resetOrganizationState', () => {
    it('should reset all state to initial values', () => {
      const { setCurrentOrganization, setOrganizations, setLoading, setError, resetOrganizationState } = store.getState();
      
      // Set some state
      setCurrentOrganization(mockOrganization1);
      setOrganizations(mockOrganizations);
      setError('Test error');
      setLoading(true); // Set loading after error to override the isLoading: false from setError

      // Verify state is set
      let state = store.getState();
      expect(state.currentOrganization).toEqual(mockOrganization1);
      expect(state.organizations).toEqual(mockOrganizations);
      expect(state.isLoading).toBe(true);
      expect(state.error).toBe('Test error');
      
      // Reset state
      resetOrganizationState();
      
      // Verify state is reset
      state = store.getState();
      expect(state.currentOrganization).toBeNull();
      expect(state.organizations).toEqual([]);
      expect(state.isLoading).toBe(false);
      expect(state.error).toBeNull();
    });
  });
});
