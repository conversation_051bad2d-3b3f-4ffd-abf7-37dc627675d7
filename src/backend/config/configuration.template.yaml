deployments:
  default_deployment: cohere_platform
  enabled_deployments:
    - cohere_platform
    - sagemaker
    - azure
    - bedrock
  sagemaker:
    region_name: us-west-2
    endpoint_name: cohere-ai
  azure:
    endpoint_url: https://cohere-ai.azurewebsites.net
  bedrock:
    region_name: us-west-2
  single_container:
    model:
    url:
database:
  url: postgresql+psycopg2://postgres:postgres@db:5432
redis:
  url: redis://:redis@redis:6379
tools:
  hybrid_web_search:
    # List of web search tool names, from: google_web_search, tavily_web_search, brave_web_search
    enabled_web_searches:
      - tavily_web_search
    # List of domains to filter (exclusively) for web search
    domain_filters:
      # - wikipedia.org
    # List of sites to filter (exclusively) for web scraping
    site_filters:
  python_interpreter:
    url: http://terrarium:8080
  gmail:
    user_scopes:
      - https://www.googleapis.com/auth/gmail.readonly
  slack:
    user_scopes:
      - search:read
  github:
    user_scopes:
      - public_repo
    default_repos:
      - cohere-ai/cohere-toolkit
  sharepoint:
    tenant_id:
  # To disable the use of the tools preamble, set it to false
  use_tools_preamble: true
feature_flags:
  # Experimental features
  use_agents_view: false
  # Community features
  use_community_features: true
auth:
  enabled_auth:
  backend_hostname: http://localhost:8000
  frontend_hostname: http://localhost:4000
logger:
  strategy: structlog
  renderer: console
  level: info
