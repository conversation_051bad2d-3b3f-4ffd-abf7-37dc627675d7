deployments:
  cohere_platform:
    api_key:
  sagemaker:
    access_key:
    secret_key:
    session_token:
  azure:
    api_key:
  bedrock:
    access_key:
    secret_key:
    session_token:
database:
  # Migrate access token, used to authenticate requests to the migrate endpoint.
  # You can generate it using some random string generator.
  migrate_token:
tools:
  tavily:
    api_key:
  wolfram_alpha:
    app_id:
  google_drive:
    client_id:
    client_secret:
    developer_key:
  tavily_web_search:
    api_key:
  brave_web_search:
    api_key:
  google_web_search:
    api_key:
    cse_id:
  slack:
    client_id:
    client_secret:
  gmail:
    client_id:
    client_secret:
  github:
    client_id:
    client_secret:
  sharepoint:
    client_id:
    client_secret:
auth:
  secret_key:
  google_oauth:
    client_id:
    client_secret:
  scim:
    username:
    password:
  oidc:
    client_id:
    client_secret:
    well_known_endpoint:
google_cloud:
  api_key:
