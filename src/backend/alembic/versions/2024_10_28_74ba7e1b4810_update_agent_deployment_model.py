"""update agent deployment model

Revision ID: 74ba7e1b4810
Revises: 20b03fd331e8
Create Date: 2024-10-28 13:27:22.299287

"""
from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = '74ba7e1b4810'
down_revision: Union[str, None] = '20b03fd331e8'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('agents', sa.Column('deployment_id', sa.String(), nullable=True))
    op.add_column('agents', sa.Column('model_id', sa.String(), nullable=True))
    op.create_foreign_key('agents_model_id_fkey', 'agents', 'models', ['model_id'], ['id'], ondelete='CASCADE')
    op.create_foreign_key('agents_deployment_id_fkey', 'agents', 'deployments', ['deployment_id'], ['id'], ondelete='CASCADE')
    # set the deployment_id and model_id for the agents using agent_deployment_model table
    # and then drop the table agent_deployment_model
    op.execute(
        """
        UPDATE agents
        SET deployment_id = agent_deployment_model.deployment_id,
            model_id = agent_deployment_model.model_id
        FROM agent_deployment_model
        WHERE agents.id = agent_deployment_model.agent_id;
        """
    )
    op.drop_table('agent_deployment_model')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('agents_deployment_id_fkey', 'agents', type_='foreignkey')
    op.drop_constraint('agents_model_id_fkey', 'agents', type_='foreignkey')
    op.drop_column('agents', 'model_id')
    op.drop_column('agents', 'deployment_id')
    # ### end Alembic commands ###
