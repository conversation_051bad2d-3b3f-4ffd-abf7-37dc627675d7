"""Remove file path

Revision ID: 08bcb9a24d9b
Revises: c301506b3676
Create Date: 2024-08-21 15:59:18.678457

"""
from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = '08bcb9a24d9b'
down_revision: Union[str, None] = 'c301506b3676'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('files', 'file_path')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('files', sa.Column('file_path', sa.VARCHAR(), autoincrement=False, nullable=False))
    # ### end Alembic commands ###
