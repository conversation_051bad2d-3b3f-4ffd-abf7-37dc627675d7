"""Adds several QOL indices

Revision ID: 2853273872ca
Revises: 6bc65982b077
Create Date: 2024-04-24 17:08:40.944132

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "2853273872ca"
down_revision: Union[str, None] = "6bc65982b077"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index("citation_message_id", "citations", ["message_id"], unique=False)
    op.create_index(
        "citation_message_id_user_id",
        "citations",
        ["message_id", "user_id"],
        unique=False,
    )
    op.create_index("citation_user_id", "citations", ["user_id"], unique=False)
    op.create_index("conversation_user_id", "conversations", ["user_id"], unique=False)
    op.create_index(
        "document_conversation_id", "documents", ["conversation_id"], unique=False
    )
    op.create_index(
        "document_conversation_id_user_id",
        "documents",
        ["conversation_id", "user_id"],
        unique=False,
    )
    op.create_index("document_message_id", "documents", ["message_id"], unique=False)
    op.create_index("document_user_id", "documents", ["user_id"], unique=False)
    op.create_index("file_conversation_id", "files", ["conversation_id"], unique=False)
    op.create_index(
        "file_conversation_id_user_id",
        "files",
        ["conversation_id", "user_id"],
        unique=False,
    )
    op.create_index("file_message_id", "files", ["message_id"], unique=False)
    op.create_index("file_user_id", "files", ["user_id"], unique=False)
    op.create_index(
        "message_conversation_id", "messages", ["conversation_id"], unique=False
    )
    op.create_index(
        "message_conversation_id_user_id",
        "messages",
        ["conversation_id", "user_id"],
        unique=False,
    )
    op.create_index("message_is_active", "messages", ["is_active"], unique=False)
    op.create_index("message_user_id", "messages", ["user_id"], unique=False)
    op.drop_column("messages", "type")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "messages",
        sa.Column("type", sa.VARCHAR(length=4), autoincrement=False, nullable=False),
    )
    op.drop_index("message_user_id", table_name="messages")
    op.drop_index("message_is_active", table_name="messages")
    op.drop_index("message_conversation_id_user_id", table_name="messages")
    op.drop_index("message_conversation_id", table_name="messages")
    op.drop_index("file_user_id", table_name="files")
    op.drop_index("file_message_id", table_name="files")
    op.drop_index("file_conversation_id_user_id", table_name="files")
    op.drop_index("file_conversation_id", table_name="files")
    op.drop_index("document_user_id", table_name="documents")
    op.drop_index("document_message_id", table_name="documents")
    op.drop_index("document_conversation_id_user_id", table_name="documents")
    op.drop_index("document_conversation_id", table_name="documents")
    op.drop_index("conversation_user_id", table_name="conversations")
    op.drop_index("citation_user_id", table_name="citations")
    op.drop_index("citation_message_id_user_id", table_name="citations")
    op.drop_index("citation_message_id", table_name="citations")
    # ### end Alembic commands ###
