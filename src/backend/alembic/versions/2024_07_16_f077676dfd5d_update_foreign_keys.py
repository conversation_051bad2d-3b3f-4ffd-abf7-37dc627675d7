"""Update foreign keys

Revision ID: f077676dfd5d
Revises: a48691a80366
Create Date: 2024-07-16 18:05:41.127076

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "f077676dfd5d"
down_revision: Union[str, None] = "a48691a80366"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "agent_tool_metadata",
        "user_id",
        existing_type=sa.TEXT(),
        type_=sa.String(),
        nullable=True,
    )
    op.alter_column(
        "agents",
        "user_id",
        existing_type=sa.TEXT(),
        type_=sa.String(),
        nullable=True,
    )
    op.alter_column(
        "tool_auth",
        "user_id",
        existing_type=sa.TEXT(),
        type_=sa.String(),
        nullable=True,
    )
    op.alter_column("citations", "user_id", existing_type=sa.VARCHAR(), nullable=True)
    op.alter_column(
        "conversations", "user_id", existing_type=sa.VARCHAR(), nullable=True
    )
    op.alter_column("documents", "user_id", existing_type=sa.VARCHAR(), nullable=True)
    op.alter_column(
        "documents", "conversation_id", existing_type=sa.VARCHAR(), nullable=True
    )
    op.alter_column("files", "user_id", existing_type=sa.VARCHAR(), nullable=True)
    op.alter_column(
        "files", "conversation_id", existing_type=sa.VARCHAR(), nullable=True
    )
    op.alter_column("messages", "user_id", existing_type=sa.VARCHAR(), nullable=True)
    op.alter_column(
        "messages", "conversation_id", existing_type=sa.VARCHAR(), nullable=True
    )
    op.alter_column(
        "snapshot_access", "user_id", existing_type=sa.VARCHAR(), nullable=True
    )
    op.alter_column(
        "snapshot_links", "user_id", existing_type=sa.VARCHAR(), nullable=True
    )
    op.alter_column("snapshots", "user_id", existing_type=sa.VARCHAR(), nullable=True)

    op.drop_constraint(
        "documents_conversation_id_fkey", "documents", type_="foreignkey"
    )
    op.drop_constraint("files_conversation_id_fkey", "files", type_="foreignkey")
    op.drop_constraint("messages_conversation_id_fkey", "messages", type_="foreignkey")
    op.drop_constraint(
        "snapshots_conversation_id_fkey", "snapshots", type_="foreignkey"
    )
    op.drop_constraint("conversations_pkey", "conversations", type_="primary")

    op.create_unique_constraint(
        "conversation_id_user_id", "conversations", ["id", "user_id"]
    )
    op.create_primary_key("conversations_pkey", "conversations", ["id", "user_id"])
    op.create_index(
        "conversation_user_id_index", "conversations", ["id", "user_id"], unique=True
    )

    op.create_foreign_key(
        None, "agent_tool_metadata", "users", ["user_id"], ["id"], ondelete="CASCADE"
    )
    op.create_foreign_key(
        None, "agents", "users", ["user_id"], ["id"], ondelete="CASCADE"
    )
    op.create_foreign_key(
        None, "citations", "users", ["user_id"], ["id"], ondelete="CASCADE"
    )
    op.create_foreign_key(
        None, "conversations", "users", ["user_id"], ["id"], ondelete="CASCADE"
    )
    op.create_foreign_key(
        "document_conversation_id_user_id_fkey",
        "documents",
        "conversations",
        ["conversation_id", "user_id"],
        ["id", "user_id"],
        ondelete="CASCADE",
    )
    op.create_foreign_key(
        "file_conversation_id_user_id_fkey",
        "files",
        "conversations",
        ["conversation_id", "user_id"],
        ["id", "user_id"],
        ondelete="CASCADE",
    )
    op.create_foreign_key(
        "message_conversation_id_user_id_fkey",
        "messages",
        "conversations",
        ["conversation_id", "user_id"],
        ["id", "user_id"],
        ondelete="CASCADE",
    )
    op.create_foreign_key(
        None, "snapshot_access", "users", ["user_id"], ["id"], ondelete="CASCADE"
    )
    op.create_foreign_key(
        None, "snapshot_links", "users", ["user_id"], ["id"], ondelete="CASCADE"
    )
    op.create_foreign_key(
        "snapshot_conversation_id_user_id_fkey",
        "snapshots",
        "conversations",
        ["conversation_id", "user_id"],
        ["id", "user_id"],
        ondelete="CASCADE",
    )
    op.create_foreign_key(
        None, "tool_auth", "users", ["user_id"], ["id"], ondelete="CASCADE"
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("conversation_user_id_index", table_name="conversations")
    op.drop_constraint("tool_auth_user_id_fkey", "tool_auth", type_="foreignkey")
    op.drop_constraint(
        "snapshot_links_user_id_fkey", "snapshot_links", type_="foreignkey"
    )
    op.drop_constraint(
        "snapshot_access_user_id_fkey", "snapshot_access", type_="foreignkey"
    )
    op.drop_constraint(
        "message_conversation_id_user_id_fkey", "messages", type_="foreignkey"
    )
    op.drop_constraint("file_conversation_id_user_id_fkey", "files", type_="foreignkey")
    op.drop_constraint(
        "document_conversation_id_user_id_fkey", "documents", type_="foreignkey"
    )
    op.drop_constraint("conversations_pkey", "conversations", type_="primary")
    op.drop_constraint("conversation_id_user_id", "conversations", type_="unique")
    op.create_primary_key("conversations_pkey", "conversations", ["id"])
    op.create_foreign_key(
        "snapshots_conversation_id_fkey",
        "snapshots",
        "conversations",
        ["conversation_id"],
        ["id"],
        ondelete="CASCADE",
    )
    op.create_foreign_key(
        "messages_conversation_id_fkey",
        "messages",
        "conversations",
        ["conversation_id"],
        ["id"],
        ondelete="CASCADE",
    )
    op.create_foreign_key(
        "files_conversation_id_fkey",
        "files",
        "conversations",
        ["conversation_id"],
        ["id"],
        ondelete="CASCADE",
    )
    op.create_foreign_key(
        "documents_conversation_id_fkey",
        "documents",
        "conversations",
        ["conversation_id"],
        ["id"],
        ondelete="CASCADE",
    )
    op.alter_column(
        "snapshots",
        "user_id",
        existing_type=sa.VARCHAR(),
        type_=sa.String(),
        nullable=False,
    )
    op.alter_column(
        "snapshot_links",
        "user_id",
        existing_type=sa.VARCHAR(),
        type_=sa.String(),
        nullable=False,
    )
    op.alter_column(
        "snapshot_access",
        "user_id",
        existing_type=sa.VARCHAR(),
        type_=sa.String(),
        nullable=False,
    )
    op.alter_column(
        "messages",
        "conversation_id",
        existing_type=sa.VARCHAR(),
        type_=sa.String(),
        nullable=False,
    )
    op.alter_column(
        "messages",
        "user_id",
        existing_type=sa.VARCHAR(),
        type_=sa.String(),
        nullable=False,
    )
    op.alter_column(
        "files",
        "conversation_id",
        existing_type=sa.VARCHAR(),
        type_=sa.String(),
        nullable=False,
    )
    op.alter_column(
        "files",
        "user_id",
        existing_type=sa.VARCHAR(),
        type_=sa.String(),
        nullable=False,
    )
    op.alter_column(
        "documents",
        "conversation_id",
        existing_type=sa.VARCHAR(),
        type_=sa.String(),
        nullable=False,
    )
    op.alter_column(
        "documents",
        "user_id",
        existing_type=sa.VARCHAR(),
        type_=sa.String(),
        nullable=False,
    )
    op.alter_column(
        "conversations",
        "user_id",
        existing_type=sa.VARCHAR(),
        type_=sa.String(),
        nullable=False,
    )
    op.alter_column(
        "citations",
        "user_id",
        existing_type=sa.VARCHAR(),
        type_=sa.String(),
        nullable=False,
    )
    op.alter_column(
        "tool_auth",
        "user_id",
        existing_type=sa.TEXT(),
        type_=sa.String(),
        nullable=False,
    )
    op.alter_column(
        "agents",
        "user_id",
        existing_type=sa.TEXT(),
        type_=sa.String(),
        nullable=False,
    )
    op.alter_column(
        "agent_tool_metadata",
        "user_id",
        existing_type=sa.TEXT(),
        type_=sa.String(),
        nullable=False,
    )
    # ### end Alembic commands ###
