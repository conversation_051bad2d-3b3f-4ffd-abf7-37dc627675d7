"""Add Agents

Revision ID: 3247f8fd3f71
Revises: c15b848babe3
Create Date: 2024-06-05 19:54:14.824484

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "3247f8fd3f71"
down_revision: Union[str, None] = "c15b848babe3"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "agents",
        sa.Column("version", sa.Integer(), nullable=False),
        sa.Column("name", sa.Text(), nullable=False),
        sa.Column("description", sa.Text(), nullable=True),
        sa.Column("preamble", sa.Text(), nullable=True),
        sa.Column("temperature", sa.Float(), nullable=False),
        sa.Column(
            "model",
            sa.Enum(
                "COMMAND_R",
                "COMMAND_R_PLUS",
                "COMMAND_LIGHT",
                "COMMAND",
                name="model",
                native_enum=False,
            ),
            nullable=False,
        ),
        sa.Column(
            "deployment",
            sa.Enum(
                "COHERE_PLATFORM",
                "SAGE_MAKER",
                "AZURE",
                "BEDROCK",
                name="deployment",
                native_enum=False,
            ),
            nullable=False,
        ),
        sa.Column("user_id", sa.Text(), nullable=False),
        sa.Column("id", sa.String(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("name", "version", name="_name_version_uc"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("agents")
    # ### end Alembic commands ###
