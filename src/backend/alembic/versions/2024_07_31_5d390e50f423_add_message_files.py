"""empty message

Revision ID: 5d390e50f423
Revises: 208f735ed937
Create Date: 2024-07-31 13:59:19.403133

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "5d390e50f423"
down_revision: Union[str, None] = "208f735ed937"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "message_files",
        sa.Column("message_id", sa.String(), nullable=False),
        sa.Column("user_id", sa.String(), nullable=False),
        sa.Column("file_id", sa.String(), nullable=False),
        sa.Column("id", sa.String(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(["message_id"], ["messages.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(["user_id"], ["users.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("message_id", "file_id", name="unique_message_file"),
    )
    op.create_index("message_file_file_id", "message_files", ["file_id"], unique=False)
    op.create_unique_constraint(None, "conversations", ["id"])
    op.drop_index("file_conversation_id", table_name="files")
    op.drop_index("file_conversation_id_user_id", table_name="files")
    op.drop_index("file_message_id", table_name="files")
    op.drop_index("file_user_id", table_name="files")
    op.drop_constraint("file_conversation_id_user_id_fkey", "files", type_="foreignkey")
    op.drop_constraint("files_message_id_fkey", "files", type_="foreignkey")
    op.drop_column("files", "message_id")
    op.drop_column("files", "conversation_id")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "files",
        sa.Column("conversation_id", sa.VARCHAR(), autoincrement=False, nullable=True),
    )
    op.add_column(
        "files",
        sa.Column("message_id", sa.VARCHAR(), autoincrement=False, nullable=True),
    )
    op.create_foreign_key(
        "files_message_id_fkey",
        "files",
        "messages",
        ["message_id"],
        ["id"],
        ondelete="CASCADE",
    )
    op.create_foreign_key(
        "file_conversation_id_user_id_fkey",
        "files",
        "conversations",
        ["conversation_id", "user_id"],
        ["id", "user_id"],
        ondelete="CASCADE",
    )
    op.create_index("file_user_id", "files", ["user_id"], unique=False)
    op.create_index("file_message_id", "files", ["message_id"], unique=False)
    op.create_index(
        "file_conversation_id_user_id",
        "files",
        ["conversation_id", "user_id"],
        unique=False,
    )
    op.create_index("file_conversation_id", "files", ["conversation_id"], unique=False)
    op.drop_constraint(None, "conversations", type_="unique")
    op.drop_index("message_file_file_id", table_name="message_files")
    op.drop_table("message_files")
    # ### end Alembic commands ###
