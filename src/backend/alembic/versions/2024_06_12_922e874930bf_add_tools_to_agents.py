"""Add tools column to Agents

Revision ID: 922e874930bf
Revises: 28763d200b29
Create Date: 2024-06-12 21:19:12.204875

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "922e874930bf"
down_revision: Union[str, None] = "28763d200b29"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("agents", sa.Column("tools", sa.JSON(), nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("agents", "tools")
    # ### end Alembic commands ###
