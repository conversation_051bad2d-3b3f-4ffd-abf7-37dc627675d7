"""Adds hashed passwords for users

Revision ID: b88f00283a27
Revises: 2853273872ca
Create Date: 2024-05-02 19:19:52.608062

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "b88f00283a27"
down_revision: Union[str, None] = "2853273872ca"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "users", sa.Column("hashed_password", sa.LargeBinary(), nullable=True)
    )
    op.create_unique_constraint("unique_user_email", "users", ["email"])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("unique_user_email", "users", type_="unique")
    op.drop_column("users", "hashed_password")
    # ### end Alembic commands ###
