"""Add Blacklist

Revision ID: 28763d200b29
Revises: a9b07acef4e8
Create Date: 2024-06-10 20:32:41.903400

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "28763d200b29"
down_revision: Union[str, None] = "a9b07acef4e8"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "blacklist",
        sa.Column("token_id", sa.String(), nullable=False),
        sa.Column("id", sa.String(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index("blacklist_token_id", "blacklist", ["token_id"], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("blacklist_token_id", table_name="blacklist")
    op.drop_table("blacklist")
    # ### end Alembic commands ###
